const express = require('express');
const router = express.Router();
const mainController = require('../controllers/mainController');
const { isAdmin } = require('../middleware/auth');

// Home page
router.get('/', mainController.getHomePage);

// About page
router.get('/about', mainController.getAboutPage);

// Values page
router.get('/values', mainController.getValuesPage);

// Contact page
router.get('/contact', mainController.getContactPage);
router.post('/contact', mainController.submitContactForm);

// Admin revenue tracking - Protected by admin middleware
router.get('/admin/revenue', isAdmin, mainController.getRevenueTracker);
router.get('/admin/cash-payments', isAdmin, mainController.getCashPayments);
router.post('/admin/cash-payments/:id', isAdmin, mainController.updateCashPayment);
router.get('/admin/api/cash-payments', isAdmin, mainController.getCashPaymentsData);

module.exports = router; 