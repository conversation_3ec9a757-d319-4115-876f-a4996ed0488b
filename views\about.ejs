<%- include('partials/header') %>

<!-- About Page Schema -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "AboutPage",
    "name": "About JunkExperts | UAE's Leading Furniture Removal Company Since 2015",
    "description": "Learn about JunkExperts - UAE's trusted furniture removal specialists since 2015. 8,000+ successful removals, 60% furniture donated, fully insured team.",
    "url": "https://www.junksexpert.com/about",
    "mainEntity": {
        "@type": "Organization",
        "name": "JunkExperts",
        "foundingDate": "2015",
        "description": "Professional junk removal and furniture disposal company serving UAE",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "22nd St, Al Quoz",
            "addressLocality": "Dubai",
            "addressCountry": "AE"
        },
        "telephone": "+971569257614",
        "email": "<EMAIL>",
        "url": "https://www.junksexpert.com",
        "sameAs": [
            "https://www.facebook.com/share/1AjgWj7njS/",
            "https://www.instagram.com/expertjunk.com5/"
        ]
    },
    "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "https://www.junksexpert.com/"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "About",
                "item": "https://www.junksexpert.com/about"
            }
        ]
    }
}
</script>

<!-- Hero Section -->
<section class="relative min-h-[80vh] flex items-center overflow-hidden first-section pb-16 md:pb-20">
    <div class="absolute inset-0 bg-black/60 z-10"></div>
    <img src="/junkservice.jpg" alt="JunkExperts professional furniture removal team in action - Dubai's leading junk removal service since 2015" class="absolute inset-0 w-full h-full object-cover object-center scale-105 hover:scale-100 transition-transform duration-7000">
    <div class="container mx-auto px-4 relative z-20 text-center">
        <h1 class="text-4xl md:text-6xl font-bold text-white mb-6 drop-shadow-lg" data-aos="fade-up">
            Furniture Removal Experts<br>
            <span class="text-orange-500 inline-block mt-2">Serving UAE Since 2015</span>
        </h1>
        <div class="max-w-3xl mx-auto" data-aos="fade-up" data-aos-delay="100">
            <p class="text-xl md:text-2xl text-orange-50 mb-8 leading-relaxed">
                Transforming spaces through responsible furniture removal and disposal
            </p>
            <div class="flex flex-col sm:flex-row justify-center gap-6">
                <a href="/booking" class="cta-primary text-lg font-semibold hover:scale-105 transition-transform">
                    Get Free Estimate
                </a>
                <a href="/values" class="cta-secondary text-lg font-semibold hover:scale-105 transition-transform">
                    Our Values
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Image Gallery Banner -->
<section class="bg-orange-500 py-8 overflow-hidden">
    <div class="container mx-auto px-4">
        <div class="image-gallery-container">
            <div class="image-gallery">
                <img src="/image1.jpg" alt="Furniture removal" class="gallery-image">
                <img src="/image2.jpg" alt="Junk removal" class="gallery-image">
                <img src="/image3.jpg" alt="Recycling" class="gallery-image">
                <img src="/image4.jpg" alt="Eco-friendly disposal" class="gallery-image">
                <img src="/image5.jpg" alt="Furniture disposal" class="gallery-image">
                <img src="/image6.jpg" alt="Professional service" class="gallery-image">
                <img src="/image7.jpg" alt="Junk removal service" class="gallery-image">
                <img src="/image8.jpg" alt="Furniture recycling" class="gallery-image">

                <!-- Duplicate images for smooth infinite scroll effect -->
                <img src="/image1.jpg" alt="Furniture removal" class="gallery-image">
                <img src="/image2.jpg" alt="Junk removal" class="gallery-image">
                <img src="/image3.jpg" alt="Recycling" class="gallery-image">
                <img src="/image4.jpg" alt="Eco-friendly disposal" class="gallery-image">
                <img src="/image5.jpg" alt="Furniture disposal" class="gallery-image">
                <img src="/image6.jpg" alt="Professional service" class="gallery-image">
                    </div>
                </div>
                    </div>
</section>

<!-- Story Section -->
<section class="py-20 md:py-28 bg-white">
    <div class="container mx-auto px-4">
        <div class="grid lg:grid-cols-2 gap-16 items-center mb-20">
            <div class="relative group" data-aos="fade-right">
                <img src="/best.jpg" alt="Our beginnings" class="rounded-2xl shadow-2xl w-full h-auto transform transition-all duration-500 group-hover:translate-x-2 group-hover:translate-y-2">
                <div class="absolute -inset-8 border-2 border-orange-500 rounded-2xl opacity-10 group-hover:opacity-100 transition duration-500 group-hover:-translate-x-2 group-hover:-translate-y-2"></div>

            </div>

            <div data-aos="fade-left">
                <h2 class="text-3xl md:text-4xl font-bold mb-8 leading-tight">
                    From Single Truck to<br>
                    <span class="text-orange-500 inline-block mt-2">Furniture Removal Leaders</span>
                </h2>
                <div class="space-y-8 text-gray-600">
                    <p class="text-lg leading-relaxed">
                        What started as a local service removing old sofas and beds has grown into UAE's most trusted furniture disposal company. Over 8,000 successful removals and counting!
                    </p>

                    <!-- New Home Junk Removal Content -->
                    <div class="bg-orange-50 p-6 rounded-xl shadow-md">
                        <h3 class="text-xl font-bold mb-4 text-gray-800">Home Junk Removal Specialists</h3>
                        <p class="text-gray-700 leading-relaxed mb-4">
                            Our specialized home junk removal services focus on safely removing and responsibly disposing of furniture items that are often challenging to handle, such as:
                        </p>
                        <div class="grid md:grid-cols-2 gap-4 mb-4">
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <h4 class="font-semibold text-orange-600 mb-2">Bed Removal</h4>
                                <p class="text-sm">Our technicians expertly disassemble and remove all bed types including king-size frames, heavy mattresses, bunk beds, and adjustable bases. We ensure all components are properly recycled or donated.</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <h4 class="font-semibold text-orange-600 mb-2">Sofa Removal</h4>
                                <p class="text-sm">From bulky sectionals to sleeper sofas, our team handles the careful removal of all upholstered furniture, navigating tight doorways and staircases without causing damage to your property.</p>
                            </div>
                        </div>
                        <p class="text-gray-700 text-sm italic">We've removed over 3,500 beds and 2,700 sofas in the past year alone, with 60% donated to families in need.</p>
                    </div>

                    <div class="grid grid-cols-2 gap-6">
                        <div class="p-6 bg-orange-50 rounded-xl shadow-md hover:shadow-xl transition-shadow">
                            <div class="text-orange-600 font-bold text-3xl mb-1">2015</div>
                            <div class="text-sm font-medium">Founded in Dubai</div>
                        </div>
                        <div class="p-6 bg-orange-50 rounded-xl shadow-md hover:shadow-xl transition-shadow">
                            <div class="text-orange-600 font-bold text-3xl mb-1">60%</div>
                            <div class="text-sm font-medium">Furniture Donated</div>
                        </div>
                    </div>
                    <p class="text-lg leading-relaxed">
                        Our journey has always focused on making furniture removal effortless while protecting our community's environment.
                    </p>
                </div>
                    </div>
                </div>

        <!-- Service Highlight Section -->
        <div class="mb-24">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">Our Specialized Services</h2>
                <p class="text-gray-600 max-w-3xl mx-auto">From single items to full property clearance, we handle it all with care and precision.</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Service 1 -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group" data-aos="fade-up">
                    <div class="h-48 overflow-hidden">
                        <img src="/images/sofa_remove.jpg" alt="Sofa Removal" class="w-full h-full object-cover group-hover:scale-105 transition-all duration-700">
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2 text-gray-800">Sofa Removal</h3>
                        <p class="text-gray-600">Safe removal of sofas of all sizes with doorway protection</p>
                    </div>
                </div>

                <!-- Service 2 -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group" data-aos="fade-up" data-aos-delay="100">
                    <div class="h-48 overflow-hidden">
                        <img src="/images/bed_remove.jpg" alt="Bed Removal" class="w-full h-full object-cover group-hover:scale-105 transition-all duration-700">
                        </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2 text-gray-800">Bed Removal</h3>
                        <p class="text-gray-600">Complete dismantling and removal of beds and mattresses</p>
                                </div>
                            </div>

                <!-- Service 3 -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group" data-aos="fade-up" data-aos-delay="200">
                    <div class="h-48 overflow-hidden">
                        <img src="/images/wardrobe_remove.jpg" alt="Wardrobe Removal" class="w-full h-full object-cover group-hover:scale-105 transition-all duration-700">
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2 text-gray-800">Wardrobe Removal</h3>
                        <p class="text-gray-600">Expert disassembly and removal of all wardrobe types</p>
                                </div>
                            </div>

                <!-- Service 4 -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group" data-aos="fade-up" data-aos-delay="300">
                    <div class="h-48 overflow-hidden">
                        <img src="/images/dining_remove.jpg" alt="Dining Set Removal" class="w-full h-full object-cover group-hover:scale-105 transition-all duration-700">
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2 text-gray-800">Dining Set Removal</h3>
                        <p class="text-gray-600">Complete dining table and chair removal services</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Values Section -->
        <div id="values" class="py-20 scroll-mt-24">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-16 leading-tight" data-aos="fade-up">
                Core Values Driving<br>
                <span class="text-orange-500 inline-block mt-2">Every Furniture Removal</span>
            </h2>

            <div class="grid md:grid-cols-3 gap-10">
                <!-- Value 1 -->
                <div class="value-card group" data-aos="fade-up">
                    <div class="p-8">
                        <div class="value-icon bg-orange-100 group-hover:bg-orange-200 transition-colors">
                            <i class="fas fa-recycle text-orange-600"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800">Eco-Conscious Removal</h3>
                        <p class="text-gray-600">70% average recycling/donation rate for all furniture</p>
                    </div>
                    <img src="/images/recycling.jpg" alt="Recycling furniture" class="value-image transform group-hover:scale-105 transition-transform duration-700">
                </div>

                <!-- Value 2 -->
                <div class="value-card group" data-aos="fade-up" data-aos-delay="100">
                    <div class="p-8">
                        <div class="value-icon bg-orange-100 group-hover:bg-orange-200 transition-colors">
                            <i class="fas fa-shield-alt text-orange-600"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800">Damage-Free Guarantee</h3>
                        <p class="text-gray-600">$2M insurance coverage for complete peace of mind</p>
                    </div>
                    <img src="/images/furniture_protection.jpg" alt="Protected furniture removal" class="value-image transform group-hover:scale-105 transition-transform duration-700">
                </div>

                <!-- Value 3 -->
                <div class="value-card group" data-aos="fade-up" data-aos-delay="200">
                    <div class="p-8">
                        <div class="value-icon bg-orange-100 group-hover:bg-orange-200 transition-colors">
                            <i class="fas fa-clock text-orange-600"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800">Same-Day Service</h3>
                        <p class="text-gray-600">90% of furniture removal jobs completed within 4 hours</p>
                    </div>
                    <img src="/image9.jpg" alt="Quick furniture removal" class="value-image transform group-hover:scale-105 transition-transform duration-700">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Team Section with Parallax -->
<section class="py-20 bg-gray-50 relative">
    <div class="absolute inset-0 bg-center bg-cover opacity-10 parallax-bg" style="background-image: url('/image10.jpg');"></div>
    <div class="container mx-auto px-4 relative z-10">
        <h2 class="text-3xl md:text-4xl font-bold text-center mb-16 leading-tight" data-aos="fade-up">
            Meet Our Furniture<br>
            <span class="text-orange-500 inline-block mt-2">Removal Specialists</span>
        </h2>

        <div class="grid md:grid-cols-3 gap-10">
            <!-- Team Member 1 -->
            <div class="team-card group" data-aos="fade-up">
                <div class="relative overflow-hidden">
                    <img src="/images/team.jpg" alt="CEO Ahmed Al Falasi" class="team-image filter group-hover:brightness-110 transition-all duration-500">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-80"></div>
                </div>
                <div class="team-details">
                    <h4 class="text-xl font-bold text-gray-800 mb-1">Ahmed Al Falasi</h4>
                    <p class="text-orange-500 font-medium mb-4">Founder & CEO</p>
                    <div class="team-achievement bg-orange-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-700">Pioneered UAE's first eco-friendly furniture removal fleet</div>
                    </div>
                </div>
            </div>

            <!-- Team Member 2 -->
            <div class="team-card group" data-aos="fade-up" data-aos-delay="100">
                <div class="relative overflow-hidden">
                    <img src="/image3.jpg" alt="Operations Manager" class="team-image filter group-hover:brightness-110 transition-all duration-500">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-80"></div>
                </div>
                <div class="team-details">
                    <h4 class="text-xl font-bold text-gray-800 mb-1">Sara Khan</h4>
                    <p class="text-orange-500 font-medium mb-4">Operations Director</p>
                    <div class="team-achievement bg-orange-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-700">Managed 5,000+ furniture removal projects</div>
                    </div>
                </div>
            </div>

            <!-- Team Member 3 -->
            <div class="team-card group" data-aos="fade-up" data-aos-delay="200">
                <div class="relative overflow-hidden">
                    <img src="/image2.jpg" alt="Sustainability Officer" class="team-image filter group-hover:brightness-110 transition-all duration-500">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-80"></div>
                </div>
                <div class="team-details">
                    <h4 class="text-xl font-bold text-gray-800 mb-1">Michael Rodriguez</h4>
                    <p class="text-orange-500 font-medium mb-4">Sustainability Lead</p>
                    <div class="team-achievement bg-orange-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-700">Developed UAE's first furniture recycling program</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Process Section -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-3xl md:text-4xl font-bold mb-4 leading-tight">
                Our Furniture Removal<br>
                <span class="text-orange-500 inline-block mt-2">Process</span>
            </h2>
        </div>

        <div class="grid md:grid-cols-4 gap-8">
            <div class="process-step hover:transform hover:-translate-y-2" data-aos="fade-up">
                <div class="process-number shadow-lg">1</div>
                <h3 class="text-lg font-bold mb-2 text-gray-800">Schedule Online</h3>
                <p class="text-gray-600 mb-4">Instant pricing for sofas, beds, and full-home clearances</p>
                <img src="/image1.jpg" alt="Online booking" class="process-image shadow-md hover:shadow-xl transition-shadow">
            </div>

            <div class="process-step hover:transform hover:-translate-y-2" data-aos="fade-up" data-aos-delay="100">
                <div class="process-number shadow-lg">2</div>
                <h3 class="text-lg font-bold mb-2 text-gray-800">Protected Removal</h3>
                <p class="text-gray-600 mb-4">Floor protection and careful disassembly</p>
                <img src="/image5.jpg" alt="Furniture removal" class="process-image shadow-md hover:shadow-xl transition-shadow">
            </div>

            <div class="process-step hover:transform hover:-translate-y-2" data-aos="fade-up" data-aos-delay="200">
                <div class="process-number shadow-lg">3</div>
                <h3 class="text-lg font-bold mb-2 text-gray-800">Eco-Processing</h3>
                <p class="text-gray-600 mb-4">Charity donations and certified recycling</p>
                <img src="/image7.jpg" alt="Recycling process" class="process-image shadow-md hover:shadow-xl transition-shadow">
            </div>

            <div class="process-step hover:transform hover:-translate-y-2" data-aos="fade-up" data-aos-delay="300">
                <div class="process-number shadow-lg">4</div>
                <h3 class="text-lg font-bold mb-2 text-gray-800">Space Transformed</h3>
                <p class="text-gray-600 mb-4">Final inspection and satisfaction guarantee</p>
                <img src="/image8.jpg" alt="Clean space" class="process-image shadow-md hover:shadow-xl transition-shadow">
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-gradient-to-br from-orange-600 to-orange-700 relative overflow-hidden">
    <div class="absolute inset-0 bg-pattern opacity-10"></div>
    <div class="container mx-auto px-4 text-center relative z-10">
        <div class="max-w-4xl mx-auto" data-aos="zoom-in">
            <h2 class="text-3xl md:text-5xl font-bold text-white mb-6 drop-shadow-md">
                Ready to Remove Unwanted Furniture?
            </h2>
            <p class="text-orange-50 text-xl mb-10 max-w-3xl mx-auto">
                Get same-day service or schedule in advance. 100% satisfaction guaranteed!
            </p>
            <div class="flex flex-col sm:flex-row justify-center gap-6">
                <a href="/booking" class="cta-primary text-lg font-semibold hover:scale-105 transition-transform duration-300">
                    <i class="fas fa-calendar-check mr-3"></i>
                    Book Now
                </a>
                <a href="tel:+971569257614" class="cta-secondary text-lg font-semibold hover:scale-105 transition-transform duration-300">
                    <i class="fas fa-phone-alt mr-3"></i>
                    Call Now
                </a>
            </div>
        </div>
    </div>
</section>

<style>
    .value-card {
        background-color: white;
        border-radius: 1rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        overflow: hidden;
        transition: all 0.3s;
    }
    .value-card:hover {
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }
    .value-icon {
        width: 4rem;
        height: 4rem;
        border-radius: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.875rem;
        margin-bottom: 1.5rem;
    }
    .value-image {
        width: 100%;
        height: 12rem;
        object-fit: cover;
        margin-top: 1.5rem;
    }
    .team-card {
        background-color: white;
        border-radius: 1rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        overflow: hidden;
        transition: all 0.3s;
    }
    .team-image {
        width: 100%;
        height: 16rem;
        object-fit: cover;
    }
    .team-details {
        padding: 1.5rem;
        text-align: center;
    }
    .process-step {
        text-align: center;
        padding: 1.5rem;
        background-color: #fff7ed;
        border-radius: 1rem;
        transition: all 0.3s;
    }
    .process-step:hover {
        background-color: white;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    .process-number {
        width: 4rem;
        height: 4rem;
        background-color: #f97316;
        color: white;
        border-radius: 9999px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        font-weight: 700;
        margin: 0 auto 1rem auto;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
    .process-image {
        width: 100%;
        height: 12rem;
        object-fit: cover;
        border-radius: 0.75rem;
        margin-top: 1rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transition: box-shadow 0.3s;
    }
    .process-image:hover {
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    .cta-primary {
        background-color: #f97316;
        color: white;
        padding: 1rem 2rem;
        border-radius: 9999px;
        transition: all 0.3s;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .cta-primary:hover {
        background-color: #ea580c;
    }
    .cta-secondary {
        background-color: white;
        color: #ea580c;
        padding: 1rem 2rem;
        border-radius: 9999px;
        transition: all 0.3s;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .cta-secondary:hover {
        background-color: #f1f5f9;
    }
    .bg-pattern {
        background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.2' fill-rule='evenodd'/%3E%3C/svg%3E");
    }

    /* Animation for image scroll */
    .animate-scroll {
        animation: scroll 30s linear infinite;
        overflow-x: hidden;
        width: 100%;
    }

    @keyframes scroll {
        0% {
            transform: translateX(0);
        }
        100% {
            transform: translateX(-100%);
        }
    }

    /* Fix for horizontal scrolling */
    html, body {
        overflow-x: hidden;
        max-width: 100%;
    }

    /* Ensure all content is properly contained */
    .container {
        max-width: 100%;
        overflow-x: hidden;
    }

    /* Fix for improved image gallery */
    .image-gallery-container {
        width: 100%;
        overflow: hidden;
    }

    .image-gallery {
        display: flex;
        gap: 1rem;
        animation: smoothScroll 30s linear infinite;
        will-change: transform;
        transform: translateZ(0);
    }

    .gallery-image {
        height: 8rem;
        width: 12rem;
        object-fit: cover;
        border-radius: 0.5rem;
        flex-shrink: 0;
    }

    @keyframes smoothScroll {
        0% {
            transform: translateX(0);
        }
        100% {
            transform: translateX(calc(-12rem * 8 - 8rem)); /* Width of 8 images plus gaps */
        }
    }

    /* Smooth scrolling optimizations */
    html {
        scroll-behavior: smooth;
    }

    /* Hardware acceleration for all animations */
    .value-card,
    .team-card,
    .process-step,
    .cta-primary,
    .cta-secondary,
    .value-image,
    .process-image {
        backface-visibility: hidden;
        transform: translateZ(0);
        will-change: transform;
    }

    /* Stable parallax */
    .parallax-bg {
        background-attachment: fixed;
        transform: translateZ(0);
        will-change: transform;
    }

    /* Prevent content jumps during animations */
    * {
        box-sizing: border-box;
    }

    /* Prevent layout shifts */
    img {
        display: block;
    }

    /* More aggressive fixes for horizontal scrolling */
    body {
        width: 100%;
        max-width: 100vw;
        position: relative;
        overflow-x: hidden;
    }

    /* Fix for container overflow */
    .container {
        width: 100%;
        max-width: 100%;
        overflow: hidden;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .gallery-image {
            height: 6rem;
            width: 9rem;
        }

        @keyframes smoothScroll {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(calc(-9rem * 8 - 8rem));
            }
        }
    }
</style>

<script>
    // Detect reduced motion preference and disable animations if needed
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        document.querySelector('.image-gallery').style.animation = 'none';
    }

    // Optimize scroll performance
    let scrollTimeout;
    window.addEventListener('scroll', function() {
        if (!scrollTimeout) {
            scrollTimeout = setTimeout(function() {
                scrollTimeout = null;
                // Disable animations during scroll
                document.body.classList.add('is-scrolling');
            }, 100);
        }
    });

    // Re-enable animations after scrolling stops
    window.addEventListener('scroll', function() {
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(function() {
            document.body.classList.remove('is-scrolling');
        }, 150);
    }, { passive: true });
</script>

<%- include('partials/footer') %>