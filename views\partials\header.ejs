<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Dynamic SEO Meta Tags -->
    <%
    // Define page-specific SEO data
    const seoData = {
        '/': {
            title: 'Professional Junk Removal Services in UAE | JunkExperts - Same Day Service',
            description: 'Expert junk removal services in Dubai, Abu Dhabi & UAE. Furniture removal, appliance disposal, eco-friendly recycling. Same-day service available. Call +971 56 925 7614',
            keywords: 'junk removal UAE, furniture removal Dubai, appliance disposal Abu Dhabi, eco-friendly junk removal, same day junk removal, professional junk removal services',
            canonical: 'https://www.junksexpert.com/',
            ogType: 'website'
        },
        '/services': {
            title: 'Furniture Removal Services Dubai | Sofa, Bed & Home Junk Removal | JunkExperts',
            description: 'Professional furniture removal in Dubai & UAE. Sofa removal, bed disposal, wardrobe removal. Eco-friendly disposal with 70% donation rate. Book online today!',
            keywords: 'furniture removal Dubai, sofa removal UAE, bed removal service, wardrobe disposal, home furniture removal, eco-friendly furniture disposal',
            canonical: 'https://www.junksexpert.com/services',
            ogType: 'service'
        },
        '/about': {
            title: 'About JunkExperts | UAE\'s Leading Furniture Removal Company Since 2015',
            description: 'Learn about JunkExperts - UAE\'s trusted furniture removal specialists since 2015. 8,000+ successful removals, 60% furniture donated, fully insured team.',
            keywords: 'about junkexperts, furniture removal company UAE, junk removal history, eco-friendly disposal company, Dubai junk removal experts',
            canonical: 'https://www.junksexpert.com/about',
            ogType: 'website'
        },
        '/contact': {
            title: 'Contact JunkExperts | Get Free Quote for Junk Removal in UAE | Call +971 56 925 7614',
            description: 'Contact JunkExperts for professional junk removal in UAE. Free quotes, same-day service. Located in Al Quoz, Dubai. Call +971 56 925 7614 or email us.',
            keywords: 'contact junk removal UAE, junk removal quote Dubai, furniture removal contact, Al Quoz junk removal, Dubai junk removal phone',
            canonical: 'https://www.junksexpert.com/contact',
            ogType: 'website'
        },
        '/booking': {
            title: 'Book Junk Removal Service Online | Schedule Furniture Removal in UAE | JunkExperts',
            description: 'Book professional junk removal service online. Easy scheduling, instant quotes, same-day service available. Furniture removal, appliance disposal in UAE.',
            keywords: 'book junk removal online, schedule furniture removal, junk removal booking UAE, online junk removal service, furniture disposal booking',
            canonical: 'https://www.junksexpert.com/booking',
            ogType: 'website'
        }
    };

    // Get current page SEO data
    const currentPath = typeof(locals) !== 'undefined' && locals.currentPath ? locals.currentPath : '/';
    const pageSEO = seoData[currentPath] || seoData['/'];
    const pageTitle = title || pageSEO.title;
    %>

    <title><%= pageTitle %></title>
    <meta name="description" content="<%= pageSEO.description %>">
    <meta name="keywords" content="<%= pageSEO.keywords %>">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="author" content="JunkExperts UAE">
    <link rel="canonical" href="<%= pageSEO.canonical %>">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<%= pageTitle %>">
    <meta property="og:description" content="<%= pageSEO.description %>">
    <meta property="og:type" content="<%= pageSEO.ogType %>">
    <meta property="og:url" content="<%= pageSEO.canonical %>">
    <meta property="og:site_name" content="JunkExperts UAE">
    <meta property="og:image" content="https://www.junksexpert.com/images/junkexperts-og-image.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:locale" content="en_AE">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<%= pageTitle %>">
    <meta name="twitter:description" content="<%= pageSEO.description %>">
    <meta name="twitter:image" content="https://www.junksexpert.com/images/junkexperts-og-image.jpg">
    <meta name="twitter:site" content="@JunkExpertsUAE">

    <!-- Additional SEO Meta Tags -->
    <meta name="geo.region" content="AE-DU">
    <meta name="geo.placename" content="Dubai">
    <meta name="geo.position" content="25.1230494;55.2214023">
    <meta name="ICBM" content="25.1230494, 55.2214023">
    <meta name="language" content="en">
    <meta name="distribution" content="global">
    <meta name="rating" content="general">
    <meta name="revisit-after" content="7 days">
    <meta name="theme-color" content="#f97316">
    <meta name="msapplication-TileColor" content="#f97316">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="JunkExperts">
    <meta name="format-detection" content="telephone=yes">
    <meta name="format-detection" content="address=yes">

    <!-- Preconnect to external domains for performance -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//unpkg.com">
    <link rel="dns-prefetch" href="//maps.google.com">

    <!-- Local Business Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        "name": "JunkExperts",
        "description": "Professional junk removal and furniture disposal services in UAE",
        "url": "https://www.junksexpert.com",
        "telephone": "+971569257614",
        "email": "<EMAIL>",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "22nd St, Al Quoz",
            "addressLocality": "Dubai",
            "addressCountry": "AE"
        },
        "geo": {
            "@type": "GeoCoordinates",
            "latitude": "25.1230494",
            "longitude": "55.2214023"
        },
        "openingHours": [
            "Mo-Sa 08:00-20:00",
            "Su 09:00-17:00"
        ],
        "serviceArea": {
            "@type": "State",
            "name": "United Arab Emirates"
        },
        "priceRange": "AED 199 - AED 2000",
        "image": "https://www.junksexpert.com/images/junkexperts-logo.jpg",
        "sameAs": [
            "https://www.facebook.com/share/1AjgWj7njS/",
            "https://www.instagram.com/expertjunk.com5/"
        ]
    }
    </script>

    <!-- Service Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Service",
        "name": "Junk Removal Services",
        "description": "Professional junk removal, furniture disposal, and eco-friendly recycling services",
        "provider": {
            "@type": "LocalBusiness",
            "name": "JunkExperts",
            "telephone": "+971569257614",
            "url": "https://www.junksexpert.com"
        },
        "serviceType": "Junk Removal",
        "areaServed": {
            "@type": "Country",
            "name": "United Arab Emirates"
        },
        "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "Junk Removal Services",
            "itemListElement": [
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Furniture Removal",
                        "description": "Professional furniture removal and disposal services"
                    }
                },
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Appliance Disposal",
                        "description": "Safe appliance removal and eco-friendly disposal"
                    }
                },
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Home Cleanout",
                        "description": "Complete home and office cleanout services"
                    }
                }
            ]
        }
    }
    </script>

    <!-- Image optimization script - preload for performance -->
    <script src="/js/image-optimization.js" defer></script>

    <!-- Preload critical resources -->
    <link rel="preload" href="https://cdn.tailwindcss.com" as="script">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" as="style">
    <link rel="preload" href="/startbg.jpg" as="image" fetchpriority="high">

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://unpkg.com">

    <!-- Inline critical CSS to avoid render blocking -->
    <style>
        /* Critical path CSS */
        body {
            font-family: 'Poppins', sans-serif;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            max-width: 100%;
            box-sizing: border-box;
        }

        h1, h2, h3, h4, h5, h6, .heading-font {
            font-family: 'Montserrat', sans-serif;
        }

        .hero-bg {
            background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)),
                        url('/startbg.jpg') center/cover;
        }

        /* Navbar height variables for consistent spacing */
        :root {
            --navbar-height: 80px;
            --navbar-height-md: 90px;
        }

        /* Global class for first section to ensure proper spacing with navbar */
        .first-section {
            padding-top: calc(var(--navbar-height) + 2rem); /* Default padding for mobile */
        }

        @media (min-width: 768px) {
            .first-section {
                padding-top: calc(var(--navbar-height-md) + 2rem); /* Increased padding for larger screens */
            }
        }

        /* Basic hover effect for all navbar links */
        .nav-link {
            position: relative;
            transition: color 0.3s ease;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: #f97316;
            transition: width 0.3s ease;
        }

        .nav-link:hover {
            color: #f97316 !important;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        /* Mobile nav hover */
        .nav-link-mobile {
            transition: color 0.3s ease, background-color 0.3s ease;
        }

        .nav-link-mobile:hover {
            color: #f97316;
            background-color: rgba(249, 115, 22, 0.05);
        }

        /* Defer non-critical CSS */
        .defer-css { visibility: hidden; }
    </style>

    <!-- Load scripts and styles -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" media="print" onload="this.media='all'">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- AOS animation library initialization -->
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script src="/js/aos-config.js" defer></script>

    <!-- Add fallbacks for users with JavaScript disabled -->
    <noscript>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css">
    </noscript>

    <script>
        // Store the current path for later use
        window.currentPath = '<%= typeof currentPath !== "undefined" ? currentPath : "/" %>';

        // Configure Tailwind theme
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'montserrat': ['Montserrat', 'sans-serif'],
                        'poppins': ['Poppins', 'sans-serif'],
                    },
                    colors: {
                        'primary': {
                            DEFAULT: '#ff7f50',
                            'dark': '#e66c41',
                            'light': '#ffa183',
                        },
                        'secondary': {
                            DEFAULT: '#1f2937',
                            'dark': '#111827',
                            'light': '#374151',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Remaining CSS moved to a separate style tag that can be deferred -->
    <style class="defer-css">
        /* Global Styles */
        body {
            font-family: 'Poppins', sans-serif;
        }

        h1, h2, h3, h4, h5, h6, .heading-font {
            font-family: 'Montserrat', sans-serif;
        }

        /* Hero Background */
        .hero-bg {
            background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)),
                        url('https://images.unsplash.com/photo-1581578731548-c64695cc6952') center/cover;
        }

        /* Navbar Styles */
        #navbar {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        #navbar.transparent {
            background-color: transparent;
            box-shadow: none;
        }

        #navbar.nav-scroll {
            background-color: rgba(255, 255, 255, 0.95);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .logo-text {
            font-weight: 700;
            letter-spacing: -0.5px;
        }

        /* Logo text is white only on homepage when navbar is transparent */
        body.is-homepage #navbar.transparent .logo-text {
            color: white;
        }

        /* But keep the orange part always primary color */
        #navbar .logo-text span {
            color: #f97316 !important;
        }

        .nav-link {
            position: relative;
            font-weight: 500;
            transition: color 0.3s ease;
            padding: 0.5rem 0;
            margin: 0 1rem; /* Increased horizontal margin */
            border-radius: 4px;
        }

        /* Only make links white on homepage and when navbar is transparent */
        body.is-homepage #navbar.transparent .nav-link {
            color: white;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: #f97316;
            transition: width 0.3s ease;
        }

        .nav-link:hover {
            color: #f97316 !important;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        /* Make sure the hover effect is consistent across all pages */
        body.is-homepage #navbar.transparent .nav-link:hover {
            color: #f97316 !important;
        }

        /* Mobile Menu Animation */
        #mobile-menu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.4s ease-out, visibility 0.4s ease-out;
            z-index: 40;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            width: 100%;
            visibility: hidden;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            border-top: 1px solid rgba(0,0,0,0.05);
            background-color: white;
        }

        #mobile-menu.open {
            max-height: 500px; /* Will be overridden by JS */
            transition: max-height 0.5s ease-in, visibility 0s;
            visibility: visible;
        }

        .nav-link-mobile {
            opacity: 0;
            transform: translateY(-10px);
            transition: opacity 0.3s ease, transform 0.3s ease, color 0.3s ease, background-color 0.3s ease;
            display: block;
            padding: 15px;
            margin: 0;
            font-weight: 500;
            border-radius: 8px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }

        .nav-link-mobile:last-child {
            border-bottom: none;
        }

        .nav-link-mobile:hover {
            color: #f97316;
            background-color: rgba(249, 115, 22, 0.05);
            padding-left: 20px;
        }

        #mobile-menu.open .nav-link-mobile {
            opacity: 1;
            transform: translateY(0);
            transition-delay: 0.1s;
        }

        /* Toggle Button Styles */
        #menu-toggle {
            position: relative;
            z-index: 50;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 10px; /* Increased padding for better touch target */
            border-radius: 8px;
        }

        #menu-toggle:hover {
            background-color: rgba(255, 127, 80, 0.1);
        }

        #menu-toggle i {
            transition: all 0.3s ease;
            color: #f97316;
        }

        /* Login button hover in desktop */
        .nav-items-container a.nav-link.login-btn:hover {
            color: #f97316 !important;
        }

        /* Book Now button hover style */
        .book-now-btn {
            transition: all 0.3s ease !important;
        }

        .book-now-btn:hover {
            background-color: #e64a19 !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(229, 74, 25, 0.3);
        }

        /* Make sure toggle is only visible on mobile */
        @media (min-width: 768px) {
            #menu-toggle {
                display: none;
            }
        }

        /* Alert styling */
        .alert-success {
            background-color: #f0fff4;
            border-color: #9ae6b4;
            color: #276749;
        }

        .alert-error {
            background-color: #fff5f5;
            border-color: #fc8181;
            color: #c53030;
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f7fafc;
        }

        ::-webkit-scrollbar-thumb {
            background: #f97316;
            border-radius: 4px;
        }

        /* Hover Effects */
        .hover-effect {
            transition: background-color 0.3s ease;
            transform: none !important;
            will-change: auto;
        }

        .hover-effect:hover {
            transform: none !important;
            box-shadow: none !important;
        }

        /* Service Card Hover */
        .service-card {
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
        }

        /* Button styles */
        .btn-primary {
            background-color: #f97316;
            color: white;
            border-radius: 9999px;
            padding: 0.5rem 1.5rem; /* Reduced padding */
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(255, 127, 80, 0.25);
        }

        .btn-primary:hover {
            background-color: #e66c41;
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(255, 127, 80, 0.3);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .container {
                padding-left: 1.25rem;
                padding-right: 1.25rem;
            }

            #navbar {
                padding: 0.5rem 0; /* Reduced padding */
            }

            #mobile-menu .container {
                padding-top: 0.75rem;
                padding-bottom: 0.75rem;
            }

            .nav-link-mobile {
                padding: 12px 15px; /* Adjusted for better spacing */
                margin: 4px 0; /* Added vertical margin */
            }

            .nav-link-mobile:hover {
                padding-left: 20px;
            }
        }

        /* Dropdown improvements */
        .dropdown-menu {
            padding: 0.5rem 0;
            margin-top: 0.5rem;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .dropdown-item {
            padding: 0.75rem 1.25rem;
            transition: all 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: rgba(249, 115, 22, 0.1);
            color: #f97316;
        }

        /* Nav spaced better */
        .nav-items-container {
            gap: 0.75rem;
        }

        /* Logo and text spacing */
        .logo-container {
            gap: 0.5rem;
            transition: transform 0.3s ease;
        }

        .logo-container:hover {
            transform: scale(1.05);
        }

        /* User menu button styles */
        .user-menu-btn {
            transition: all 0.3s ease;
        }

        .user-menu-btn:hover {
            background-color: #f1f5f9;
            transform: translateY(-1px);
        }
    </style>

    <!-- Script to make deferred CSS visible when loaded -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Make deferred CSS visible
            document.querySelectorAll('.defer-css').forEach(function(el) {
                el.classList.remove('defer-css');
            });

            const navbar = document.getElementById('navbar');
            const mainBody = document.getElementById('main-body');
            const path = window.location.pathname;
            const menuToggle = document.getElementById('menu-toggle');
            const mobileMenu = document.getElementById('mobile-menu');
            const menuIconBars = document.getElementById('menu-icon-bars');
            const menuIconClose = document.getElementById('menu-icon-close');

            // Add homepage class if we're on the homepage
            if (path === '/') {
                mainBody.classList.add('is-homepage');
                navbar.classList.add('transparent');
            }

            // Add scroll event listener to change nav background on home page
            window.addEventListener('scroll', function() {
                if (path === '/') {
                    if (window.scrollY > 50) {
                        navbar.classList.remove('transparent');
                        navbar.classList.add('nav-scroll');
                    } else {
                        navbar.classList.add('transparent');
                        navbar.classList.remove('nav-scroll');
                    }
                }
            });

            // Track menu state explicitly
            let menuIsOpen = false;

            // Mobile menu toggle - fixed implementation
            menuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Toggle state using our explicit variable
                menuIsOpen = !menuIsOpen;

                if (menuIsOpen) {
                    // Open menu
                    menuIconBars.classList.add('hidden');
                    menuIconClose.classList.remove('hidden');
                    mobileMenu.classList.add('open');
                    mobileMenu.setAttribute('aria-hidden', 'false');
                    menuToggle.setAttribute('aria-expanded', 'true');

                    // Set height based on content
                    const height = mobileMenu.scrollHeight;
                    mobileMenu.style.maxHeight = height + 'px';
                } else {
                    // Close menu
                    menuIconClose.classList.add('hidden');
                    menuIconBars.classList.remove('hidden');
                    mobileMenu.style.maxHeight = '0px';
                    mobileMenu.setAttribute('aria-hidden', 'true');
                    menuToggle.setAttribute('aria-expanded', 'false');

                    // Delay the class removal to allow animation to complete
                    setTimeout(() => {
                        mobileMenu.classList.remove('open');
                    }, 300);
                }
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', function(e) {
                if (menuIsOpen &&
                    !mobileMenu.contains(e.target) &&
                    !menuToggle.contains(e.target)) {

                    // Close menu
                    menuIsOpen = false;
                    menuIconClose.classList.add('hidden');
                    menuIconBars.classList.remove('hidden');
                    mobileMenu.style.maxHeight = '0px';
                    mobileMenu.setAttribute('aria-hidden', 'true');
                    menuToggle.setAttribute('aria-expanded', 'false');

                    setTimeout(() => {
                        mobileMenu.classList.remove('open');
                    }, 300);
                }
            });

            // Adjust mobile menu height on window resize
            window.addEventListener('resize', function() {
                if (menuIsOpen) {
                    const height = mobileMenu.scrollHeight;
                    mobileMenu.style.maxHeight = height + 'px';
                }
            });

            // Auto-hide alerts after 5 seconds
            const alerts = document.querySelectorAll('#alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    alert.style.transition = 'opacity 0.5s ease';
                    setTimeout(() => {
                        alert.style.display = 'none';
                    }, 500);
                }, 5000);
            });
        });
    </script>
</head>
<body class="text-gray-800" id="main-body">
    <!-- Navigation -->
    <nav class="fixed w-full z-50 transition-all duration-300" id="navbar">
        <div class="container mx-auto px-6 py-2 flex items-center justify-between">
            <!-- Logo -->
            <a href="/" class="flex items-center logo-container">
                <div class="h-10 w-10 rounded-full overflow-hidden shadow-md border-2 border-orange-500">
                    <img src="https://d1csarkz8obe9u.cloudfront.net/posterpreviews/junk-removal-logo-junk-removal-services-icon-design-template-113e6dbca3b9794444bd7fc5a46e0817_screen.jpg?ts=1683623837" alt="Logo" class="h-full w-full object-cover">
                </div>
                <span class="ml-3 text-xl logo-text cursor-pointer">Junk<span class="text-primary">Experts</span></span>
            </a>

            <!-- Desktop Menu -->
            <div class="hidden md:flex items-center nav-items-container">
                <a href="/" class="nav-link text-sm py-1 px-2">Home</a>
                <a href="/services" class="nav-link text-sm py-1 px-2">Services</a>
                <a href="/about" class="nav-link text-sm py-1 px-2">About</a>
                <a href="/contact" class="nav-link text-sm py-1 px-2">Contact</a>

                <% if (user && user.isAuthenticated) { %>
                    <%
                    // Check if we're on the landing page and should hide admin links
                    const isLandingPage = typeof currentPath !== "undefined" && currentPath === "/";
                    // Only show admin links if:
                    // 1. User has admin role AND
                    // 2. Not on landing page OR explicitly logged in (has login timestamp)
                    const showAdminLinks = user.role === 'admin' && (!isLandingPage || (user.loginTime && Date.now() - user.loginTime < 3600000));
                    %>

                    <% if (showAdminLinks) { %>
                        <a href="/admin/dashboard" class="nav-link text-sm py-1 px-2">Dashboard</a>
                    <% } %>
                    <div class="relative group ml-4">
                        <button class="flex items-center rounded-full bg-gray-100 px-4 py-1.5 text-xs font-medium text-gray-700 hover:bg-gray-200 transition-all user-menu-btn">
                            <span><%= user.name %></span>
                            <i class="fas fa-chevron-down ml-2 text-xs"></i>
                        </button>
                        <div class="absolute right-0 mt-3 w-48 bg-white rounded-lg shadow-lg overflow-hidden invisible opacity-0 group-hover:visible group-hover:opacity-100 transition-all duration-300 z-50 dropdown-menu">
                            <a href="#" onclick="event.preventDefault(); window.location.href='/auth/logout?t=' + new Date().getTime();" class="block px-5 py-2 text-sm text-gray-700 hover:bg-orange-100 dropdown-item">Logout</a>
                        </div>
                    </div>
                <% } else { %>
                    <a href="/auth/login" class="nav-link text-sm py-1 px-2 login-btn">Login</a>
                <% } %>

                <a href="/booking" class="bg-orange-600 text-white ml-4 text-sm py-1.5 px-4 rounded-full text-lg hover:bg-orange-700 transition book-now-btn">
                    Book Now
                </a>
            </div>

            <!-- Mobile Menu Toggle Button -->
            <button id="menu-toggle" class="md:hidden focus:outline-none" aria-expanded="false">
                <i class="fas fa-bars text-xl" id="menu-icon-bars"></i>
                <i class="fas fa-times text-xl hidden" id="menu-icon-close"></i>
            </button>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden bg-white shadow-lg w-full" aria-hidden="true" role="menu">
            <div class="container mx-auto px-6 py-5 space-y-4">
                <a href="/" class="block nav-link-mobile" role="menuitem">Home</a>
                <a href="/services" class="block nav-link-mobile" role="menuitem">Services</a>
                <a href="/about" class="block nav-link-mobile" role="menuitem">About</a>
                <a href="/contact" class="block nav-link-mobile" role="menuitem">Contact</a>

                <% if (user && user.isAuthenticated) { %>
                    <%
                    // Check if we're on the landing page and should hide admin links
                    const mobileLandingPage = typeof currentPath !== "undefined" && currentPath === "/";
                    // Only show admin links if:
                    // 1. User has admin role AND
                    // 2. Not on landing page OR explicitly logged in (has login timestamp)
                    const showMobileAdminLinks = user.role === 'admin' && (!mobileLandingPage || (user.loginTime && Date.now() - user.loginTime < 3600000));
                    %>

                    <% if (showMobileAdminLinks) { %>
                        <a href="/admin/dashboard" class="block nav-link-mobile" role="menuitem">Dashboard</a>
                    <% } %>
                    <div class="border-t border-gray-200 pt-5 mt-5">
                        <div class="flex items-center justify-between">
                            <span class="font-medium"><%= user.name %></span>
                            <a href="#" onclick="event.preventDefault(); window.location.href='/auth/logout?t=' + new Date().getTime();" class="text-red-500 text-sm font-medium py-2 px-4 rounded-lg hover:bg-red-50 transition" role="menuitem">Logout</a>
                        </div>
                    </div>
                <% } else { %>
                    <a href="/auth/login" class="block nav-link-mobile" role="menuitem">Login</a>
                <% } %>

                <div class="pt-3">
                    <a href="/booking" class="block bg-orange-600 text-white px-6 py-3.5 rounded-full hover:bg-orange-700 transition duration-300 transform hover:translate-y-[-2px] hover:shadow-lg text-center font-medium mt-2 book-now-btn" role="menuitem">
                        Book Now
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Alert Messages -->
    <% if (typeof error !== 'undefined' && error) { %>
        <div class="fixed top-24 right-4 z-50 alert-error px-6 py-3 rounded-lg shadow-md" id="alert">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle mr-2"></i>
                <%= error %>
            </div>
        </div>
    <% } %>

    <% if (typeof success !== 'undefined' && success) { %>
        <div class="fixed top-24 right-4 z-50 alert-success px-6 py-3 rounded-lg shadow-md" id="alert">
            <div class="flex items-center">
                <i class="fas fa-check-circle mr-2"></i>
                <%= success %>
            </div>
        </div>
    <% } %>
</body>
</html>