<%- include('partials/header') %>

<!-- Contact Page Schema -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "ContactPage",
    "name": "Contact JunkExperts | Get Free Quote for Junk Removal in UAE",
    "description": "Contact JunkExperts for professional junk removal in UAE. Free quotes, same-day service. Located in Al Quoz, Dubai.",
    "url": "https://www.junksexpert.com/contact",
    "mainEntity": {
        "@type": "LocalBusiness",
        "name": "JunkExperts",
        "telephone": "+971569257614",
        "email": "<EMAIL>",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "22nd St, Al Quoz",
            "addressLocality": "Dubai",
            "addressCountry": "AE"
        },
        "geo": {
            "@type": "GeoCoordinates",
            "latitude": "25.1230494",
            "longitude": "55.2214023"
        },
        "openingHours": [
            "Mo-Sa 08:00-20:00",
            "Su 09:00-17:00"
        ]
    },
    "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "https://www.junksexpert.com/"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Contact",
                "item": "https://www.junksexpert.com/contact"
            }
        ]
    }
}
</script>

<!-- Contact Hero Section -->
<section class="relative overflow-hidden pt-32 pb-20">
    <!-- Background Image -->
    <div class="absolute inset-0 z-0">
        <img src="/best.jpg" alt="Contact us background" class="w-full h-full object-cover opacity-20">
    </div>

    <!-- Overlay Gradient -->
    <div class="absolute inset-0 bg-gradient-to-br from-orange-600 to-orange-700 opacity-90 z-5"></div>

    <!-- Animated Background Elements -->
    <div class="absolute inset-0 overflow-hidden z-10">
        <div class="blob blob-1"></div>
        <div class="blob blob-2"></div>
    </div>

    <div class="container mx-auto px-4 md:px-6 relative z-20 text-center">
        <div class="flex flex-col items-center" data-aos="fade-up">
            <div class="inline-block p-3 bg-white/20 rounded-full mb-5 backdrop-blur-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
            </div>
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-4 drop-shadow-lg">Get In Touch</h1>
        <div class="w-24 h-1 bg-white mx-auto mb-6"></div>
            <p class="text-lg md:text-xl text-orange-50 max-w-3xl mx-auto leading-relaxed" data-aos="fade-up" data-aos-delay="100">
            Have questions or need assistance with your junk removal needs? Our team is here to help you every step of the way.
        </p>
        </div>

        <!-- Contact Method Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 max-w-5xl mx-auto">
            <!-- Call Card -->
            <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 hover:bg-white/20 transition-all shadow-lg flex flex-col items-center" data-aos="fade-up" data-aos-delay="100">
                <div class="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mb-4 shadow-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                </div>
                <h3 class="text-white text-xl font-bold mb-2">Call Us</h3>
                <p class="text-orange-50 mb-4 text-center">Speak directly with our team</p>
                <a href="tel:+971569257614" class="text-white font-medium hover:text-orange-200 transition">+971 56 925 7614</a>
            </div>

            <!-- Email Card -->
            <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 hover:bg-white/20 transition-all shadow-lg flex flex-col items-center" data-aos="fade-up" data-aos-delay="200">
                <div class="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mb-4 shadow-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                </div>
                <h3 class="text-white text-xl font-bold mb-2">Email Us</h3>
                <p class="text-orange-50 mb-4 text-center">Send us a detailed message</p>
                <a href="mailto:<EMAIL>" class="text-white font-medium hover:text-orange-200 transition"><EMAIL></a>
            </div>

            <!-- Visit Card -->
            <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 hover:bg-white/20 transition-all shadow-lg flex flex-col items-center" data-aos="fade-up" data-aos-delay="300">
                <div class="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mb-4 shadow-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                </div>
                <h3 class="text-white text-xl font-bold mb-2">Visit Us</h3>
                <p class="text-orange-50 mb-4 text-center">Come to our office</p>
                <p class="text-white text-center text-sm">22nd St, Al Quoz<br>Dubai, UAE</p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Information and Form Section -->
<section class="py-16 md:py-24 bg-white relative">
    <div class="absolute top-0 left-0 right-0 h-20 bg-gradient-to-b from-gray-100 to-white"></div>
    <div class="container mx-auto px-4 md:px-6 relative z-10">
        <div class="max-w-6xl mx-auto">
            <div class="grid md:grid-cols-5 gap-8 mb-16">
                <!-- Contact Information -->
                <div class="md:col-span-2 bg-gradient-to-br from-orange-600 to-orange-500 text-white rounded-2xl shadow-xl overflow-hidden relative" data-aos="fade-up">
                    <div class="p-8 md:p-10 relative z-10">
                        <h2 class="text-2xl font-bold mb-6 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            Contact Information
                        </h2>
                        <p class="mb-8 text-orange-50">
                            Reach out to us using any of the following methods or fill out the contact form.
                        </p>

                        <div class="space-y-6">
                            <div class="flex items-start">
                                <div class="bg-orange-700 rounded-lg p-3 mr-4 flex-shrink-0">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-bold text-lg mb-1">Address</h3>
                                    <p class="text-orange-50">22nd St, Al Quoz<br>Dubai, UAE</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <div class="bg-orange-700 rounded-lg p-3 mr-4 flex-shrink-0">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-bold text-lg mb-1">Phone</h3>
                                    <p class="text-orange-50">+971 56 925 7614</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <div class="bg-orange-700 rounded-lg p-3 mr-4 flex-shrink-0">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-bold text-lg mb-1">Email</h3>
                                    <p class="text-orange-50"><EMAIL></p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <div class="bg-orange-700 rounded-lg p-3 mr-4 flex-shrink-0">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-bold text-lg mb-1">Business Hours</h3>
                                    <p class="text-orange-50">Monday - Saturday: 8AM - 8PM<br>Sunday: 9AM - 5PM</p>
                                </div>
                            </div>
                        </div>

                        <div class="mt-10">
                            <h3 class="font-bold text-lg mb-4">Follow Us</h3>
                            <div class="flex space-x-4">
                                <a href="#" class="bg-orange-700 hover:bg-orange-800 text-white p-3 rounded-lg transition-colors hover:scale-110 transform duration-200">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
                                    </svg>
                                </a>
                                <a href="#" class="bg-orange-700 hover:bg-orange-800 text-white p-3 rounded-lg transition-colors hover:scale-110 transform duration-200">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                    </svg>
                                </a>
                                <a href="#" class="bg-orange-700 hover:bg-orange-800 text-white p-3 rounded-lg transition-colors hover:scale-110 transform duration-200">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                                    </svg>
                                </a>
                                <a href="#" class="bg-orange-700 hover:bg-orange-800 text-white p-3 rounded-lg transition-colors hover:scale-110 transform duration-200">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div class="md:col-span-3 bg-white rounded-2xl shadow-xl p-8 md:p-10 relative" data-aos="fade-up" data-aos-delay="100">

                    <div class="relative z-10">
                        <!-- Form Header -->
                        <div class="flex items-center mb-8">
                            <div class="bg-orange-100 p-3 rounded-xl mr-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                                </svg>
                            </div>
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900">Send Us a Message</h2>
                                <p class="text-gray-600">We'll get back to you within 24 hours</p>
                            </div>
                        </div>

                        <!-- Contact Form -->
                        <form action="/contact" method="POST" class="space-y-6">
                            <div>
                                <label class="block text-gray-700 mb-2 font-medium" for="name">Your Name *</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                        </svg>
                                    </div>
                                    <input type="text" id="name" name="name" class="w-full pl-10 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent" required>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-gray-700 mb-2 font-medium" for="email">Email Address *</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                        <input type="email" id="email" name="email" class="w-full pl-10 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent" required>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-gray-700 mb-2 font-medium" for="phone">Phone Number</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                            </svg>
                                        </div>
                                        <input type="tel" id="phone" name="phone" class="w-full pl-10 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label class="block text-gray-700 mb-2 font-medium" for="subject">Subject *</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                                        </svg>
                                    </div>
                                    <input type="text" id="subject" name="subject" class="w-full pl-10 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent" required>
                                </div>
                            </div>

                            <div>
                                <label class="block text-gray-700 mb-2 font-medium" for="message">Message *</label>
                                <div class="relative">
                                    <div class="absolute top-3 left-3 flex items-start pointer-events-none">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                        </svg>
                                    </div>
                                    <textarea id="message" name="message" rows="5" class="w-full pl-10 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent" required></textarea>
                        </div>
                        </div>

                            <div class="flex items-center">
                                <input type="checkbox" id="privacy" name="privacy" class="h-4 w-4 text-orange-500 focus:ring-orange-500 border-gray-300 rounded" required>
                                <label for="privacy" class="ml-2 block text-sm text-gray-700">
                                    I agree to the <a href="#" class="text-orange-600 hover:text-orange-800">Privacy Policy</a>
                                </label>
                        </div>

                            <button type="submit" class="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white py-3 px-8 rounded-lg shadow-md hover:shadow-lg transition-all font-medium flex items-center w-full sm:w-auto justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                            </svg>
                            Send Message
                        </button>
                    </form>
                    </div>
                </div>
            </div>

            <!-- Map Section -->
            <div class="mb-16 bg-white rounded-2xl shadow-xl overflow-hidden" data-aos="fade-up">
                <div class="grid md:grid-cols-5 gap-0">
                    <!-- Map Info -->
                    <div class="md:col-span-2 p-8 md:p-10 flex flex-col justify-center bg-orange-50">
                        <div class="mb-6">
                            <div class="inline-block p-3 bg-orange-100 rounded-xl mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Our Location</h2>
                            <p class="text-gray-600 mb-6">Located in Al Quoz, Dubai, our office is easily accessible by public transportation and has ample parking nearby.</p>

                            <div class="space-y-4 mb-8">
                                <div class="flex items-center text-gray-700">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                                    </svg>
                                    <span>22nd St, Al Quoz, Dubai</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                    </svg>
                                    <span>+971 56 925 7614</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                                    </svg>
                                    <span><EMAIL></span>
                                </div>
                            </div>

                            <a href="https://maps.google.com?q=22nd+St+Al+Quoz+Dubai+UAE" target="_blank" class="inline-flex items-center text-orange-600 hover:text-orange-800 font-medium group transition-colors">
                                <span>Get Directions</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 transform group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- Map Display -->
                    <div class="md:col-span-3 h-full min-h-[400px] relative overflow-hidden bg-gray-100">
                        <!-- Google Maps Interactive Embed -->
                        <iframe
                            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3612.373828575674!2d55.2214023!3d25.1230494!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e5f695fbe01480f%3A0x2d32b7010163efdd!2s22nd%20St%20-%20Al%20Quoz%20-%20Dubai%20-%20United%20Arab%20Emirates!5e0!3m2!1sen!2s!4v1745078640673!5m2!1sen!2s"
                            class="absolute inset-0 w-full h-full border-0 map-iframe"
                            allowfullscreen=""
                            loading="lazy"
                            referrerpolicy="no-referrer-when-downgrade">
                        </iframe>

                        <!-- Map Overlay -->
                        <div class="absolute inset-0 flex items-center justify-center pointer-events-none">
                            <div class="text-center p-8 bg-white/80 backdrop-blur-sm rounded-xl shadow-lg max-w-md pointer-events-auto">
                                <div class="w-16 h-16 bg-orange-500 text-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-md">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                </div>
                                <h3 class="text-xl font-bold mb-2 text-gray-800">JunkExperts Headquarters</h3>
                                <p class="text-gray-600 mb-6">22nd St, Al Quoz<br>Dubai, UAE</p>
                                <a href="https://maps.google.com?q=22nd+St+Al+Quoz+Dubai+UAE" target="_blank" class="inline-flex items-center justify-center bg-orange-500 hover:bg-orange-600 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                                    </svg>
                                    Open in Google Maps
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- FAQs Section -->
            <div class="mb-16" data-aos="fade-up">
                <div class="text-center mb-12">
                    <div class="inline-block p-3 bg-orange-100 rounded-xl mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
                    <p class="text-gray-600 max-w-3xl mx-auto">Find answers to our most commonly asked questions. If you need further assistance, please don't hesitate to contact us.</p>
                </div>

                <div class="grid md:grid-cols-2 gap-6">
                    <!-- FAQ Item 1 -->
                    <div class="bg-white p-8 rounded-xl shadow-md hover:shadow-lg transition-shadow border-l-4 border-orange-500">
                        <div class="flex items-start">
                            <div class="bg-orange-100 p-3 rounded-xl mr-4 flex-shrink-0">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-900">What areas do you service?</h3>
                        <p class="text-gray-700">We provide junk removal services throughout Dubai, Abu Dhabi, Sharjah, and other major areas in the UAE. Contact us for availability in your specific location.</p>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 2 -->
                    <div class="bg-white p-8 rounded-xl shadow-md hover:shadow-lg transition-shadow border-l-4 border-orange-500">
                        <div class="flex items-start">
                            <div class="bg-orange-100 p-3 rounded-xl mr-4 flex-shrink-0">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-900">How much does junk removal cost?</h3>
                        <p class="text-gray-700">Our pricing depends on the volume and type of junk to be removed. We offer free estimates, and our prices start from AED 199 for small jobs.</p>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 3 -->
                    <div class="bg-white p-8 rounded-xl shadow-md hover:shadow-lg transition-shadow border-l-4 border-orange-500">
                        <div class="flex items-start">
                            <div class="bg-orange-100 p-3 rounded-xl mr-4 flex-shrink-0">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-900">How quickly can you remove my junk?</h3>
                        <p class="text-gray-700">We offer same-day and next-day service in most areas. Contact us for immediate assistance or book online for a scheduled appointment.</p>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 4 -->
                    <div class="bg-white p-8 rounded-xl shadow-md hover:shadow-lg transition-shadow border-l-4 border-orange-500">
                        <div class="flex items-start">
                            <div class="bg-orange-100 p-3 rounded-xl mr-4 flex-shrink-0">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-900">What items do you not accept?</h3>
                        <p class="text-gray-700">We do not accept hazardous materials like chemicals, asbestos, or biohazards. Contact us if you're unsure about a specific item.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- CTA Section -->
            <div class="relative bg-gradient-to-br from-orange-600 to-orange-500 text-white p-12 rounded-2xl shadow-xl overflow-hidden" data-aos="fade-up">
                <!-- Background Image -->
                <div class="absolute inset-0 z-0">
                    <img src="/image5.jpg" alt="Contact us now" class="w-full h-full object-cover opacity-20">
                </div>

                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-10 pattern-dots"></div>

                <!-- Content -->
                <div class="relative z-10">
                    <div class="flex flex-col md:flex-row items-center justify-between max-w-5xl mx-auto">
                        <div class="mb-8 md:mb-0 md:mr-8 text-center md:text-left">
                            <h2 class="text-3xl md:text-4xl font-bold mb-4">Need Immediate Assistance?</h2>
                            <p class="text-xl mb-0 max-w-2xl mx-auto md:mx-0">
                        For urgent junk removal needs, call us directly for the fastest response.
                    </p>
                        </div>
                        <div class="text-center">
                            <a href="tel:+971569257614" class="bg-white text-orange-600 px-8 py-4 rounded-lg text-lg hover:bg-orange-50 transition hover:shadow-lg inline-flex items-center font-medium transform hover:scale-105 transition-transform duration-300">
                                <i class="fas fa-phone-alt mr-3"></i>
                                Call Now: +971 56 925 7614
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Animated Background Styles -->
<style>
    .blob {
        position: absolute;
        border-radius: 50%;
        filter: blur(60px);
        opacity: 0.3;
        animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        animation-iteration-count: infinite;
        animation-direction: alternate;
        will-change: transform;
        transform: translateZ(0);
    }

    .blob-1 {
        width: 400px;
        height: 400px;
        background-color: rgba(249, 115, 22, 0.7);
        top: -100px;
        left: -100px;
        animation-name: blobAnimation1;
        animation-duration: 15s;
    }

    .blob-2 {
        width: 350px;
        height: 350px;
        background-color: rgba(234, 88, 12, 0.7);
        top: 10%;
        right: -50px;
        animation-name: blobAnimation2;
        animation-duration: 18s;
    }

    @keyframes blobAnimation1 {
        0% { transform: translate(0, 0) scale(1); }
        100% { transform: translate(50px, 20px) scale(1.2); }
    }

    @keyframes blobAnimation2 {
        0% { transform: translate(0, 0) scale(1); }
        100% { transform: translate(-30px, 40px) scale(1.15); }
    }

    /* Dot pattern background */
    .pattern-dots {
        background-image: radial-gradient(currentColor 1px, transparent 1px);
        background-size: 20px 20px;
    }

    /* Smooth scrolling & optimization */
    html {
        scroll-behavior: smooth;
    }

    /* Fix for horizontal scrolling */
    html, body {
        overflow-x: hidden;
        max-width: 100%;
        position: relative;
    }

    /* Hardware acceleration for animations */
    .blob,
    .transition-all,
    .hover\:scale-105,
    .hover\:shadow-lg {
        backface-visibility: hidden;
        transform: translateZ(0);
        will-change: transform, opacity, box-shadow;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .blob {
            filter: blur(40px);
            opacity: 0.25;
        }

        .blob-1, .blob-2 {
            width: 200px;
            height: 200px;
        }
    }

    /* Map enhancements */
    .map-container {
        transition: all 0.5s ease-in-out;
    }

    .map-container.map-loaded {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .map-overlay {
        transition: opacity 0.7s ease-in-out;
    }

    /* Additional responsive improvements */
    @media (max-width: 768px) {
        .map-container {
            min-height: 350px;
        }
    }
</style>

<!-- Map Enhancement Script -->
<script>
    // Check if user prefers reduced motion
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    // Function to be called when iframe is loaded
    function enhanceMapExperience() {
        // Add smooth fade-in effect to the map overlay
        setTimeout(() => {
            const mapOverlay = document.querySelector('.map-overlay');
            if (mapOverlay) {
                mapOverlay.classList.add('opacity-100');
            }
        }, 500);

        // Add map loaded class to parent for additional styling if needed
        const mapContainer = document.querySelector('.map-container');
        if (mapContainer) {
            mapContainer.classList.add('map-loaded');
        }
    }

    // Add appropriate classes to elements when page loads
    document.addEventListener('DOMContentLoaded', () => {
        const mapFrame = document.querySelector('.map-iframe');
        if (mapFrame) {
            // Mark the appropriate elements
            const mapDisplay = mapFrame.closest('.md\\:col-span-3');
            if (mapDisplay) {
                mapDisplay.classList.add('map-container');
            }

            // Mark the overlay
            const overlay = mapDisplay?.querySelector('.bg-white\\/80');
            if (overlay?.parentElement) {
                overlay.parentElement.classList.add('map-overlay', 'opacity-0', 'transition-opacity', 'duration-700');
            }

            // Set iframe class and load event
            mapFrame.classList.add('map-iframe');
            mapFrame.addEventListener('load', enhanceMapExperience);
        }
    });

    // Detect reduced motion preference
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        document.querySelectorAll('.blob').forEach(blob => {
            blob.style.animation = 'none';
        });
        document.querySelector('html').style.scrollBehavior = 'auto';
    }

    // Optimize animation performance
    let scrollTimeout;
    window.addEventListener('scroll', function() {
        if (!scrollTimeout) {
            scrollTimeout = setTimeout(function() {
                scrollTimeout = null;
                // Disable animations during scroll
                document.body.classList.add('is-scrolling');
            }, 100);
        }
    });

    // Re-enable animations after scrolling stops
    window.addEventListener('scroll', function() {
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(function() {
            document.body.classList.remove('is-scrolling');
        }, 150);
    }, { passive: true });

    // Style for scrolling state
    document.head.insertAdjacentHTML('beforeend', `
        <style>
            .is-scrolling .blob {
                animation-play-state: paused;
            }

            .is-scrolling .transition-all,
            .is-scrolling .hover\\:scale-105 {
                transition: none !important;
            }
        </style>
    `);
</script>

<%- include('partials/footer') %>