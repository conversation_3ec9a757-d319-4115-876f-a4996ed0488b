<%- include('partials/header') %>

<!-- Homepage Specific Schema -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Professional Junk Removal Services in UAE | JunkExperts",
    "description": "Expert junk removal services in Dubai, Abu Dhabi & UAE. Furniture removal, appliance disposal, eco-friendly recycling. Same-day service available.",
    "url": "https://www.junksexpert.com/",
    "mainEntity": {
        "@type": "LocalBusiness",
        "name": "JunkExperts",
        "description": "Professional junk removal and furniture disposal services in UAE",
        "telephone": "+971569257614",
        "email": "<EMAIL>",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "22nd St, Al Quoz",
            "addressLocality": "Dubai",
            "addressCountry": "AE"
        }
    }
}
</script>

<!-- FAQ Schema for Homepage -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
        {
            "@type": "Question",
            "name": "What areas do you service in UAE?",
            "acceptedAnswer": {
                "@type": "Answer",
                "text": "We provide junk removal services throughout Dubai, Abu Dhabi, Sharjah, and other major areas in the UAE. We offer same-day service in most locations."
            }
        },
        {
            "@type": "Question",
            "name": "How much does junk removal cost?",
            "acceptedAnswer": {
                "@type": "Answer",
                "text": "Our pricing depends on the volume and type of junk to be removed. We offer free estimates, and our prices start from AED 199 for small jobs. Contact us for a personalized quote."
            }
        },
        {
            "@type": "Question",
            "name": "Do you offer same-day junk removal service?",
            "acceptedAnswer": {
                "@type": "Answer",
                "text": "Yes, we offer same-day and next-day service in most areas across UAE. Contact us for immediate assistance or book online for a scheduled appointment."
            }
        },
        {
            "@type": "Question",
            "name": "What items do you remove?",
            "acceptedAnswer": {
                "@type": "Answer",
                "text": "We remove furniture, appliances, electronics, mattresses, and general household junk. We handle everything from single items to complete home cleanouts with eco-friendly disposal methods."
            }
        }
    ]
}
</script>

<!-- Enhanced Modern UI Design -->
<style>
    /* Core Variables - Enhanced Color Palette */
    :root {
        --primary: #f97316;
        --primary-dark: #ea580c;
        --primary-light: #fdba74;
        --primary-ultra-light: #fff7ed;
        --secondary: #334155;
        --secondary-dark: #1e293b;
        --secondary-light: #64748b;
        --light: #f8fafc;
        --dark: #0f172a;
        --gray: #64748b;
        --gray-light: #e2e8f0;
        --white: #ffffff;
        --success: #10b981;
        --warning: #f59e0b;
        --error: #ef4444;
        --info: #3b82f6;
        --border-radius: 8px;
        --border-radius-lg: 12px;
        --border-radius-xl: 16px;
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        --transition-fast: 0.15s ease;
        --transition-normal: 0.3s ease;
        --transition-slow: 0.5s ease;
    }

    /* Basic Reset */
    *, *::before, *::after {
        box-sizing: border-box;
    }

    /* Enhanced Typography */
    body {
        font-family: 'Poppins', 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        line-height: 1.6;
        color: var(--dark);
        background-color: var(--light);
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    h1, h2, h3, h4, h5, h6 {
        margin-top: 0;
        margin-bottom: 0.5rem;
        font-family: 'Montserrat', 'Segoe UI', Roboto, sans-serif;
        font-weight: 700;
        line-height: 1.2;
        letter-spacing: -0.02em;
    }

    h1 {
        font-size: 2.75rem;
        line-height: 1.1;
    }

    h2 {
        font-size: 2.25rem;
        line-height: 1.2;
    }

    h3 {
        font-size: 1.75rem;
        line-height: 1.3;
    }

    p {
        margin-top: 0;
        margin-bottom: 1rem;
        font-size: 1.05rem;
    }

    @media (max-width: 768px) {
        h1 {
            font-size: 2.25rem;
        }

        h2 {
            font-size: 1.875rem;
        }

        h3 {
            font-size: 1.5rem;
        }

        p {
            font-size: 1rem;
        }
    }

    /* Container */
    .container {
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
        margin-right: auto;
        margin-left: auto;
    }

    @media (min-width: 576px) {
        .container {
            max-width: 540px;
        }
    }

    @media (min-width: 768px) {
        .container {
            max-width: 720px;
        }
    }

    @media (min-width: 992px) {
        .container {
            max-width: 960px;
        }
    }

    @media (min-width: 1200px) {
        .container {
            max-width: 1140px;
        }
    }

    /* Grid System */
    .row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -15px;
        margin-left: -15px;
    }

    .col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
    .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12,
    .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12,
    .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {
        position: relative;
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
    }

    .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .col-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .col-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }

    .col-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }

    @media (min-width: 576px) {
        .col-sm-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }
    }

    @media (min-width: 768px) {
        .col-md-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }

        .col-md-4 {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }

        .col-md-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }
    }

    @media (min-width: 992px) {
        .col-lg-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }

        .col-lg-4 {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }

        .col-lg-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }
    }

    /* Enhanced Buttons with Modern Styling */
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: 0.75rem 1.75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: 50px;
        transition: all var(--transition-normal);
        text-decoration: none;
        position: relative;
        overflow: hidden;
        z-index: 1;
        box-shadow: var(--shadow-sm);
    }

    .btn::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.1);
        transform: scaleX(0);
        transform-origin: right;
        transition: transform var(--transition-normal);
        z-index: -1;
    }

    .btn:hover::after {
        transform: scaleX(1);
        transform-origin: left;
    }

    .btn-primary {
        color: var(--white);
        background-color: var(--primary);
        border-color: var(--primary);
        box-shadow: 0 4px 14px rgba(249, 115, 22, 0.25);
    }

    .btn-primary:hover {
        background-color: var(--primary-dark);
        border-color: var(--primary-dark);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(249, 115, 22, 0.35);
    }

    .btn-primary:active {
        transform: translateY(0);
        box-shadow: 0 4px 8px rgba(249, 115, 22, 0.25);
    }

    .btn-outline-light {
        color: var(--white);
        border-color: rgba(255, 255, 255, 0.6);
        background-color: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(4px);
    }

    .btn-outline-light:hover {
        color: var(--dark);
        background-color: var(--white);
        border-color: var(--white);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 255, 255, 0.25);
    }

    .btn-outline-light:active {
        transform: translateY(0);
        box-shadow: 0 4px 8px rgba(255, 255, 255, 0.15);
    }

    .btn i, .btn svg {
        margin-right: 0.5rem;
        font-size: 1.1em;
        transition: transform var(--transition-fast);
    }

    .btn:hover i, .btn:hover svg {
        transform: translateX(2px);
    }

    /* Enhanced Cards with Modern Styling */
    .card {
        position: relative;
        display: flex;
        flex-direction: column;
        min-width: 0;
        word-wrap: break-word;
        background-color: var(--white);
        background-clip: border-box;
        border: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow);
        transition: transform var(--transition-normal), box-shadow var(--transition-normal);
        overflow: hidden;
        will-change: transform, box-shadow;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .card-img-top {
        width: 100%;
        border-top-left-radius: calc(var(--border-radius-lg) - 1px);
        border-top-right-radius: calc(var(--border-radius-lg) - 1px);
        height: 220px;
        object-fit: cover;
        transition: transform var(--transition-normal);
    }

    .card:hover .card-img-top {
        transform: scale(1.05);
    }

    .card-body {
        flex: 1 1 auto;
        padding: 1.5rem;
        position: relative;
        z-index: 1;
        background-color: var(--white);
    }

    .card-title {
        margin-bottom: 0.75rem;
        font-weight: 700;
        color: var(--dark);
        position: relative;
        display: inline-block;
    }

    .card-title::after {
        content: '';
        position: absolute;
        bottom: -4px;
        left: 0;
        width: 40px;
        height: 3px;
        background-color: var(--primary);
        border-radius: 3px;
        transition: width var(--transition-normal);
    }

    .card:hover .card-title::after {
        width: 60px;
    }

    .card-text {
        margin-bottom: 1.25rem;
        color: var(--gray);
        line-height: 1.6;
    }

    .card-footer {
        padding: 1rem 1.5rem;
        background-color: rgba(0, 0, 0, 0.02);
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }

    /* Card with hover overlay */
    .card-overlay {
        position: relative;
        overflow: hidden;
    }

    .card-overlay::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0) 60%);
        z-index: 1;
        opacity: 0;
        transition: opacity var(--transition-normal);
    }

    .card-overlay:hover::before {
        opacity: 1;
    }

    .card-overlay-content {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        padding: 1.5rem;
        z-index: 2;
        transform: translateY(20px);
        opacity: 0;
        transition: transform var(--transition-normal), opacity var(--transition-normal);
    }

    .card-overlay:hover .card-overlay-content {
        transform: translateY(0);
        opacity: 1;
    }

    /* Enhanced Section Styling */
    .section {
        padding: 5rem 0;
        position: relative;
        overflow: hidden;
    }

    .section-lg {
        padding: 7rem 0;
    }

    .section-sm {
        padding: 3rem 0;
    }

    .section-title {
        position: relative;
        margin-bottom: 3rem;
        z-index: 1;
    }

    .section-title h2 {
        position: relative;
        display: inline-block;
        margin-bottom: 1rem;
    }

    .section-title h2::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 0;
        width: 60px;
        height: 4px;
        background-color: var(--primary);
        border-radius: 2px;
    }

    .section-title.text-center h2::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .section-subtitle {
        font-size: 1.1rem;
        color: var(--gray);
        max-width: 700px;
        margin: 0 auto;
    }

    /* Section with background shapes */
    .section-shapes {
        position: relative;
    }

    .section-shapes::before,
    .section-shapes::after {
        content: '';
        position: absolute;
        border-radius: 50%;
        z-index: 0;
    }

    .section-shapes::before {
        top: -100px;
        right: -100px;
        width: 300px;
        height: 300px;
        background-color: rgba(249, 115, 22, 0.05);
    }

    .section-shapes::after {
        bottom: -100px;
        left: -100px;
        width: 250px;
        height: 250px;
        background-color: rgba(51, 65, 85, 0.05);
    }

    /* Section dividers */
    .section-divider {
        position: relative;
        height: 100px;
        overflow: hidden;
    }

    .section-divider-wave {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z' fill='%23ffffff'/%3E%3C/svg%3E");
        background-size: cover;
        background-position: center;
    }

    .section-divider-slant {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M1200 120L0 16.48 0 0 1200 0 1200 120z' fill='%23ffffff'/%3E%3C/svg%3E");
        background-size: cover;
        background-position: center;
    }

    /* Backgrounds */
    .bg-primary {
        background-color: var(--primary) !important;
    }

    .bg-secondary {
        background-color: var(--secondary) !important;
    }

    .bg-dark {
        background-color: var(--dark) !important;
    }

    .bg-light {
        background-color: var(--light) !important;
    }

    .bg-white {
        background-color: var(--white) !important;
    }

    /* Text colors */
    .text-primary {
        color: var(--primary) !important;
    }

    .text-secondary {
        color: var(--secondary) !important;
    }

    .text-white {
        color: var(--white) !important;
    }

    .text-dark {
        color: var(--dark) !important;
    }

    .text-gray {
        color: var(--gray) !important;
    }

    /* Utilities */
    .d-none {
        display: none !important;
    }

    .d-block {
        display: block !important;
    }

    .d-flex {
        display: flex !important;
    }

    @media (min-width: 768px) {
        .d-md-none {
            display: none !important;
        }

        .d-md-block {
            display: block !important;
        }

        .d-md-flex {
            display: flex !important;
        }
    }

    .flex-column {
        flex-direction: column !important;
    }

    .flex-row {
        flex-direction: row !important;
    }

    @media (min-width: 768px) {
        .flex-md-row {
            flex-direction: row !important;
        }
    }

    .justify-content-center {
        justify-content: center !important;
    }

    .justify-content-between {
        justify-content: space-between !important;
    }

    .align-items-center {
        align-items: center !important;
    }

    .text-center {
        text-align: center !important;
    }

    .text-left {
        text-align: left !important;
    }

    .text-right {
        text-align: right !important;
    }

    .rounded {
        border-radius: var(--border-radius) !important;
    }

    .rounded-circle {
        border-radius: 50% !important;
    }

    .shadow {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    .shadow-sm {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
    }

    .shadow-lg {
        box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
    }

    .w-100 {
        width: 100% !important;
    }

    .h-100 {
        height: 100% !important;
    }

    .mx-auto {
        margin-right: auto !important;
        margin-left: auto !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .mt-0 {
        margin-top: 0 !important;
    }

    .mr-0 {
        margin-right: 0 !important;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .ml-0 {
        margin-left: 0 !important;
    }

    .m-1 {
        margin: 0.25rem !important;
    }

    .mt-1 {
        margin-top: 0.25rem !important;
    }

    .mr-1 {
        margin-right: 0.25rem !important;
    }

    .mb-1 {
        margin-bottom: 0.25rem !important;
    }

    .ml-1 {
        margin-left: 0.25rem !important;
    }

    .m-2 {
        margin: 0.5rem !important;
    }

    .mt-2 {
        margin-top: 0.5rem !important;
    }

    .mr-2 {
        margin-right: 0.5rem !important;
    }

    .mb-2 {
        margin-bottom: 0.5rem !important;
    }

    .ml-2 {
        margin-left: 0.5rem !important;
    }

    .m-3 {
        margin: 1rem !important;
    }

    .mt-3 {
        margin-top: 1rem !important;
    }

    .mr-3 {
        margin-right: 1rem !important;
    }

    .mb-3 {
        margin-bottom: 1rem !important;
    }

    .ml-3 {
        margin-left: 1rem !important;
    }

    .m-4 {
        margin: 1.5rem !important;
    }

    .mt-4 {
        margin-top: 1.5rem !important;
    }

    .mr-4 {
        margin-right: 1.5rem !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    .ml-4 {
        margin-left: 1.5rem !important;
    }

    .m-5 {
        margin: 3rem !important;
    }

    .mt-5 {
        margin-top: 3rem !important;
    }

    .mr-5 {
        margin-right: 3rem !important;
    }

    .mb-5 {
        margin-bottom: 3rem !important;
    }

    .ml-5 {
        margin-left: 3rem !important;
    }

    .p-0 {
        padding: 0 !important;
    }

    .pt-0 {
        padding-top: 0 !important;
    }

    .pr-0 {
        padding-right: 0 !important;
    }

    .pb-0 {
        padding-bottom: 0 !important;
    }

    .pl-0 {
        padding-left: 0 !important;
    }

    .p-1 {
        padding: 0.25rem !important;
    }

    .pt-1 {
        padding-top: 0.25rem !important;
    }

    .pr-1 {
        padding-right: 0.25rem !important;
    }

    .pb-1 {
        padding-bottom: 0.25rem !important;
    }

    .pl-1 {
        padding-left: 0.25rem !important;
    }

    .p-2 {
        padding: 0.5rem !important;
    }

    .pt-2 {
        padding-top: 0.5rem !important;
    }

    .pr-2 {
        padding-right: 0.5rem !important;
    }

    .pb-2 {
        padding-bottom: 0.5rem !important;
    }

    .pl-2 {
        padding-left: 0.5rem !important;
    }

    .p-3 {
        padding: 1rem !important;
    }

    .pt-3 {
        padding-top: 1rem !important;
    }

    .pr-3 {
        padding-right: 1rem !important;
    }

    .pb-3 {
        padding-bottom: 1rem !important;
    }

    .pl-3 {
        padding-left: 1rem !important;
    }

    .p-4 {
        padding: 1.5rem !important;
    }

    .pt-4 {
        padding-top: 1.5rem !important;
    }

    .pr-4 {
        padding-right: 1.5rem !important;
    }

    .pb-4 {
        padding-bottom: 1.5rem !important;
    }

    .pl-4 {
        padding-left: 1.5rem !important;
    }

    .p-5 {
        padding: 3rem !important;
    }

    .pt-5 {
        padding-top: 3rem !important;
    }

    .pr-5 {
        padding-right: 3rem !important;
    }

    .pb-5 {
        padding-bottom: 3rem !important;
    }

    .pl-5 {
        padding-left: 3rem !important;
    }

    .position-relative {
        position: relative !important;
    }

    .position-absolute {
        position: absolute !important;
    }

    .position-fixed {
        position: fixed !important;
    }

    .fixed-top {
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        z-index: 1030;
    }

    .fixed-bottom {
        position: fixed;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 1030;
    }

    .top-0 {
        top: 0 !important;
    }

    .right-0 {
        right: 0 !important;
    }

    .bottom-0 {
        bottom: 0 !important;
    }

    .left-0 {
        left: 0 !important;
    }

    /* Fix for WhatsApp button */
    .whatsapp-btn::after {
        display: none !important;
    }
</style>

<!-- Enhanced Modern Hero Section with Dynamic Elements -->
<section class="section-lg position-relative overflow-hidden" id="home">
  <div class="position-absolute top-0 left-0 w-100 h-100" style="background: linear-gradient(rgba(15,23,42,0.85), rgba(15,23,42,0.9)), url('/startbg.jpg') center/cover no-repeat fixed; z-index: 0;"></div>
  <div class="container position-relative" style="z-index: 1;">
    <div class="row align-items-center" style="min-height: 100vh;">
      <div class="col-lg-6 mb-5 mb-lg-0" data-aos="fade-right" data-aos-delay="100">
        <div class="d-inline-flex align-items-center px-4 py-2 bg-primary rounded-pill text-white mb-4 shadow-lg">
          <span class="pulse-dot mr-2"></span>
          <span class="text-uppercase" style="font-size: 0.875rem; font-weight: 600; letter-spacing: 1px;">Dubai's #1 Junk Removal & Transport</span>
        </div>
        <h1 class="text-white mb-4" style="font-size: 3.5rem; line-height: 1.2; font-weight: 800; letter-spacing: -0.02em;">
          <span class="text-primary position-relative d-inline-block">
            Modern Waste Collection
            <span class="position-absolute bottom-0 left-0 w-100 h-1 bg-primary" style="transform: skewX(-15deg);"></span>
          </span>
          <span class="d-block mt-2">Eco-Friendly Disposal</span>
        </h1>
        <p class="text-white mb-5" style="font-size: 1.25rem; line-height: 1.6; opacity: 0.9; max-width: 540px;">
          Professional, eco-friendly, and hassle-free solutions for all your waste removal and transportation needs across the UAE.
        </p>
        <div class="d-flex flex-column flex-md-row gap-3 mb-5">
          <a href="/booking" class="btn btn-primary btn-lg px-5 py-3 shadow-lg d-inline-flex align-items-center">
            <i class="fas fa-clipboard-list mr-2"></i>
            Book Now
          </a>
          <a href="/services" class="btn btn-outline-light btn-lg px-5 py-3 d-inline-flex align-items-center">
            <i class="fas fa-couch mr-2"></i>
            Our Services
          </a>
        </div>
        <div class="d-flex flex-wrap align-items-center gap-4 mt-4">
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4" style="min-width: 200px;">
            <div class="d-flex align-items-center">
              <div class="bg-primary rounded-lg p-3 d-flex align-items-center justify-content-center shadow-md" style="width: 48px; height: 48px;">
                <i class="fas fa-users text-white"></i>
              </div>
              <div class="ms-3">
                <p class="text-white mb-0">Trusted by</p>
                <p class="text-white mb-0" style="font-weight: 700; font-size: 1.5rem;">500+ Clients</p>
              </div>
            </div>
          </div>
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4" style="min-width: 200px;">
            <div class="d-flex align-items-center">
              <div class="bg-primary rounded-lg p-3 d-flex align-items-center justify-content-center shadow-md" style="width: 48px; height: 48px;">
                <i class="fas fa-star text-white"></i>
              </div>
              <div class="ms-3">
                <div class="d-flex align-items-center mb-1">
                  <div class="text-warning me-2">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star-half-alt"></i>
                  </div>
                  <span class="text-white" style="font-weight: 700; font-size: 1.25rem;">4.8/5</span>
                </div>
                <p class="text-white mb-0" style="font-size: 0.875rem; text-transform: uppercase; letter-spacing: 0.5px;">Based on 200+ reviews</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-6 d-none d-lg-block" data-aos="fade-left" data-aos-delay="200">
        <div class="position-relative">
          <div class="rounded-3 overflow-hidden shadow-xl" style="transform: perspective(1000px) rotateY(-5deg);">
            <img src="/image3.jpg" alt="Professional junk removal team loading furniture into truck in Dubai - JunkExperts eco-friendly waste collection service" class="w-100" style="height: 500px; object-fit: cover;">
          </div>
          <div class="position-absolute bg-white rounded-3 p-4 shadow-lg" style="bottom: -20px; right: -20px; max-width: 250px;">
            <div class="d-flex align-items-center">
              <div class="bg-primary rounded-circle p-3 d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                <i class="fas fa-recycle text-white"></i>
              </div>
              <div class="ms-3">
                <p class="text-gray-600 mb-0" style="font-size: 0.875rem; text-transform: uppercase; letter-spacing: 0.5px;">Eco-friendly</p>
                <p class="text-dark mb-0" style="font-weight: 700; font-size: 1.1rem;">Disposal</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
    /* Hero Section Animations */
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }

    .pulse-dot {
        width: 8px;
        height: 8px;
        background-color: #fff;
        border-radius: 50%;
        position: relative;
    }

    .pulse-dot::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        background-color: #fff;
        border-radius: 50%;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); opacity: 1; }
        70% { transform: scale(2); opacity: 0; }
        100% { transform: scale(1); opacity: 0; }
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        h1 {
            font-size: 2.5rem !important;
        }

        .btn-lg {
            padding: 0.75rem 1.5rem !important;
        }
    }
</style>

<!-- Add Hero Section Animations -->
<style>
    /* Hero Title Animation */
    .hero-title {
        font-size: 3rem;
        line-height: 1.2;
        font-weight: 800;
        letter-spacing: -0.02em;
    }

    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.25rem;
        }
    }

    .hero-highlight {
        display: inline-block;
        position: relative;
    }

    .hero-highlight::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 30%;
        background-color: rgba(249, 115, 22, 0.2);
        z-index: -1;
        transform: skewX(-15deg);
    }

    /* Pulse Dot Animation */
    .pulse-dot {
        width: 10px;
        height: 10px;
        background-color: #fff;
        border-radius: 50%;
        position: relative;
        display: inline-block;
    }

    .pulse-dot::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        background-color: #fff;
        border-radius: 50%;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
            opacity: 1;
        }
        70% {
            transform: scale(2);
            opacity: 0;
        }
        100% {
            transform: scale(1);
            opacity: 0;
        }
    }

    /* Stars Animation */
    .stars-container {
        color: #ffc107;
        display: inline-flex;
        position: relative;
    }

    .stars-container i {
        margin-right: 2px;
        animation: star-pulse 3s infinite;
    }

    .stars-container i:nth-child(1) { animation-delay: 0s; }
    .stars-container i:nth-child(2) { animation-delay: 0.3s; }
    .stars-container i:nth-child(3) { animation-delay: 0.6s; }
    .stars-container i:nth-child(4) { animation-delay: 0.9s; }
    .stars-container i:nth-child(5) { animation-delay: 1.2s; }

    @keyframes star-pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.2); }
    }

    /* Scroll Indicator Animation */
    .chevron-container {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        animation: bounce 2s infinite;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }

    /* Particles Animation */
    .particle {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
    }

    .particle-1 {
        width: 100px;
        height: 100px;
        top: 20%;
        left: 10%;
        animation: float 15s infinite;
    }

    .particle-2 {
        width: 150px;
        height: 150px;
        top: 60%;
        left: 15%;
        animation: float 20s infinite;
        animation-delay: 2s;
    }

    .particle-3 {
        width: 80px;
        height: 80px;
        top: 30%;
        right: 20%;
        animation: float 18s infinite;
        animation-delay: 1s;
    }

    .particle-4 {
        width: 120px;
        height: 120px;
        bottom: 20%;
        right: 10%;
        animation: float 22s infinite;
        animation-delay: 3s;
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0) translateX(0);
        }
        25% {
            transform: translateY(-30px) translateX(30px);
        }
        50% {
            transform: translateY(-15px) translateX(-20px);
        }
        75% {
            transform: translateY(20px) translateX(10px);
        }
    }

    /* Hero Image Animations */
    .hero-image-container {
        transform: perspective(1000px) rotateY(-5deg);
        transition: transform 0.5s ease;
    }

    .hero-image-container:hover {
        transform: perspective(1000px) rotateY(0deg);
    }

    .hero-main-image {
        transition: transform 0.5s ease, box-shadow 0.5s ease;
    }

    .hero-image-container:hover .hero-main-image {
        transform: rotate(0deg);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    }

    .feature-badge, .service-badge {
        transition: transform 0.3s ease;
    }

    .hero-image-container:hover .feature-badge {
        transform: translateY(-5px);
    }

    .hero-image-container:hover .service-badge {
        transform: rotate(0deg) translateY(-5px);
    }

    /* CTA Button Animations */
    .cta-buttons .btn {
        transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
    }

    .cta-buttons .btn:hover {
        transform: translateY(-5px);
    }

    .cta-buttons .btn:active {
        transform: translateY(-2px);
    }

    /* Trust Indicators Animation */
    .trust-indicators > div {
        transition: transform 0.3s ease;
    }

    .trust-indicators > div:hover {
        transform: translateY(-5px);
    }
</style>

<!-- Enhanced Services Section with Modern Design -->
<section class="py-24 bg-gray-50 section-shapes" id="services">
    <div class="container mx-auto px-4 sm:px-6">
        <!-- Section Header with Enhanced Typography -->
        <div class="text-center mb-20 section-title" data-aos="fade-up">
            <div class="inline-flex items-center px-4 py-2 bg-primary-ultra-light text-primary rounded-full text-sm font-semibold mb-4 shadow-sm">
                <span class="w-2 h-2 bg-primary rounded-full mr-2"></span>
                <span style="letter-spacing: 1px;">OUR EXPERTISE</span>
            </div>
            <h2 class="text-3xl md:text-4xl font-extrabold mb-6 relative inline-block">
                Our Premium Services
                <div class="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-primary rounded-full"></div>
            </h2>
            <p class="text-gray-600 max-w-2xl mx-auto text-lg section-subtitle">
                Comprehensive solutions for all your waste removal and transportation needs with eco-friendly disposal methods
            </p>

            <!-- Decorative Elements -->
            <div class="absolute top-0 right-0 -mt-10 -mr-10 w-32 h-32 bg-primary opacity-5 rounded-full"></div>
            <div class="absolute bottom-0 left-0 -mb-10 -ml-10 w-24 h-24 bg-primary opacity-5 rounded-full"></div>
        </div>

        <!-- Enhanced Featured Service with Advanced Hover Effects -->
        <div class="bg-white rounded-2xl shadow-xl overflow-hidden mb-20 transform transition-all duration-500 hover:shadow-2xl group relative" data-aos="fade-up">
            <!-- Decorative Background Elements -->
            <div class="absolute top-0 right-0 w-64 h-64 bg-primary-ultra-light rounded-full opacity-50 transform translate-x-1/3 -translate-y-1/3 z-0"></div>
            <div class="absolute bottom-0 left-0 w-48 h-48 bg-primary-ultra-light rounded-full opacity-30 transform -translate-x-1/3 translate-y-1/3 z-0"></div>

            <div class="grid md:grid-cols-2 items-stretch relative z-1">
                <!-- Enhanced Content Side -->
                <div class="p-8 md:p-12 lg:p-16 flex flex-col justify-center relative z-10">
                    <!-- Animated Icon Container -->
                    <div class="w-20 h-20 bg-primary-ultra-light rounded-2xl flex items-center justify-center text-primary mb-8
                         shadow-md transform transition-all duration-500 group-hover:bg-primary group-hover:text-white
                         group-hover:rotate-6 group-hover:scale-110">
                        <i class="fas fa-truck text-2xl"></i>
                    </div>

                    <!-- Enhanced Title with Animation -->
                    <h3 class="text-2xl md:text-3xl font-bold mb-4 text-gray-800 relative inline-block">
                        Furniture Removal
                        <span class="absolute bottom-0 left-0 w-0 h-1 bg-primary rounded transition-all duration-500 group-hover:w-full"></span>
                    </h3>

                    <p class="text-gray-600 mb-8 text-lg leading-relaxed">
                        Our professional team handles all types of furniture removal with care and efficiency. We ensure your items are properly disposed of or recycled according to environmental standards.
                    </p>

                    <!-- Enhanced Feature Grid -->
                    <div class="grid md:grid-cols-2 gap-6 mb-10">
                        <div class="flex items-start transform transition-all duration-300 hover:-translate-y-1">
                            <div class="flex-shrink-0 mt-1">
                                <div class="w-8 h-8 rounded-lg bg-green-100 flex items-center justify-center text-green-600 shadow-sm">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <span class="text-gray-800 font-semibold block mb-1">Same-day service</span>
                                <p class="text-gray-500 text-sm">Quick response time for urgent needs</p>
                            </div>
                        </div>
                        <div class="flex items-start transform transition-all duration-300 hover:-translate-y-1">
                            <div class="flex-shrink-0 mt-1">
                                <div class="w-8 h-8 rounded-lg bg-green-100 flex items-center justify-center text-green-600 shadow-sm">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <span class="text-gray-800 font-semibold block mb-1">Eco-friendly disposal</span>
                                <p class="text-gray-500 text-sm">Sustainable practices for the environment</p>
                            </div>
                        </div>
                        <div class="flex items-start transform transition-all duration-300 hover:-translate-y-1">
                            <div class="flex-shrink-0 mt-1">
                                <div class="w-8 h-8 rounded-lg bg-green-100 flex items-center justify-center text-green-600 shadow-sm">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <span class="text-gray-800 font-semibold block mb-1">Professional team</span>
                                <p class="text-gray-500 text-sm">Trained and experienced staff</p>
                            </div>
                        </div>
                        <div class="flex items-start transform transition-all duration-300 hover:-translate-y-1">
                            <div class="flex-shrink-0 mt-1">
                                <div class="w-8 h-8 rounded-lg bg-green-100 flex items-center justify-center text-green-600 shadow-sm">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <span class="text-gray-800 font-semibold block mb-1">Transparent pricing</span>
                                <p class="text-gray-500 text-sm">No hidden fees or surprises</p>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced CTA Button -->
                    <a href="/booking" class="inline-flex items-center justify-center px-8 py-4 bg-primary text-white font-semibold rounded-xl
                       shadow-lg hover:bg-primary-dark transform transition-all duration-500 hover:-translate-y-2 hover:shadow-xl
                       relative overflow-hidden group-cta">
                        <span class="relative z-10">Book This Service</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 relative z-10 transition-transform duration-300 transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                        </svg>
                        <div class="absolute inset-0 bg-primary-dark transform scale-x-0 origin-left transition-transform duration-500 group-hover:scale-x-100"></div>
                    </a>
                </div>

                <!-- Enhanced Image Side with Advanced Overlay -->
                <div class="relative overflow-hidden group-image">
                    <!-- Overlay with Enhanced Animation -->
                    <div class="absolute inset-0 bg-gradient-to-r from-primary/90 to-primary-dark/80
                         opacity-0 group-hover:opacity-100 transition-all duration-500
                         flex items-center justify-center z-10 transform translate-y-full group-hover:translate-y-0">
                        <div class="text-white text-center p-10 transform translate-y-10 opacity-0 transition-all duration-500 delay-100
                             group-hover:translate-y-0 group-hover:opacity-100">
                            <h4 class="text-2xl font-bold mb-4">Professional Service</h4>
                            <p class="mb-6 text-white/90 max-w-xs mx-auto">Our trained staff uses proper equipment to ensure safe and efficient furniture removal.</p>
                            <a href="/services/furniture-removal" class="inline-block px-6 py-3 border-2 border-white rounded-lg font-medium
                               hover:bg-white hover:text-primary transition-all duration-300 transform hover:-translate-y-1">
                                Learn More
                            </a>
                        </div>
                    </div>

                    <!-- Enhanced Image with Zoom Effect -->
                    <div class="h-full min-h-[450px] bg-cover bg-center transform transition-transform duration-700 group-hover:scale-110"
                         style="background-image: url('/images/sofa_remove.jpg');">
                    </div>

                    <!-- Image Corner Decorations -->
                    <div class="absolute top-4 right-4 w-16 h-16 border-t-4 border-r-4 border-white opacity-60 z-10"></div>
                    <div class="absolute bottom-4 left-4 w-16 h-16 border-b-4 border-l-4 border-white opacity-60 z-10"></div>
                </div>
            </div>
        </div>

        <!-- Enhanced Service Cards with Modern Design -->
        <div class="grid md:grid-cols-3 gap-10" data-aos="fade-up" data-aos-delay="100">
            <!-- Enhanced Service Card 1 -->
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-500 hover:-translate-y-3 hover:shadow-xl group card-hover-effect">
                <!-- Enhanced Card Header with Image and Overlay -->
                <div class="h-56 bg-cover bg-center relative overflow-hidden">
                    <!-- Image with Zoom Effect -->
                    <div class="absolute inset-0 bg-cover bg-center transform transition-transform duration-700 group-hover:scale-110"
                         style="background-image: url('/image1.jpg');">
                    </div>

                    <!-- Gradient Overlay -->
                    <div class="absolute inset-0 bg-gradient-to-b from-transparent via-black/20 to-black/70 opacity-80 transition-opacity duration-500 group-hover:opacity-90"></div>

                    <!-- Badge with Animation -->
                    <div class="absolute top-4 left-4 z-10 transform transition-transform duration-500 group-hover:scale-110 group-hover:rotate-3">
                        <span class="bg-primary text-white text-xs font-bold px-4 py-1.5 rounded-full shadow-md inline-flex items-center">
                            <span class="w-1.5 h-1.5 bg-white rounded-full mr-1.5 animate-pulse"></span>
                            POPULAR
                        </span>
                    </div>

                    <!-- Card Title Overlay -->
                    <div class="absolute bottom-0 left-0 right-0 p-6 z-10 transform translate-y-5 transition-transform duration-500 group-hover:translate-y-0">
                        <h3 class="text-xl font-bold text-white mb-1 group-hover:text-primary-light transition-colors duration-300">Bed & Mattress Removal</h3>
                        <div class="w-10 h-1 bg-primary rounded-full transform scale-x-0 origin-left transition-transform duration-500 group-hover:scale-x-100"></div>
                    </div>
                </div>

                <!-- Enhanced Card Content -->
                <div class="p-6 pt-8 relative">
                    <!-- Floating Icon -->
                    <div class="w-16 h-16 bg-primary-ultra-light rounded-2xl flex items-center justify-center text-primary -mt-16 mb-6
                         shadow-lg border-4 border-white absolute right-6 transform transition-all duration-500
                         group-hover:bg-primary group-hover:text-white group-hover:rotate-6 group-hover:scale-110">
                        <i class="fas fa-bed text-xl"></i>
                    </div>

                    <!-- Card Description -->
                    <p class="text-gray-600 mb-6 mt-6">
                        We handle all types of beds and mattresses, from king size to bunk beds with professional care and eco-friendly disposal.
                    </p>

                    <!-- Enhanced Features List -->
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center transform transition-all duration-300 hover:-translate-x-1 hover:text-primary">
                            <div class="w-6 h-6 rounded-lg bg-green-100 flex items-center justify-center text-green-600 mr-3 shadow-sm">
                                <svg class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <span class="text-gray-700 font-medium">All mattress types</span>
                        </li>
                        <li class="flex items-center transform transition-all duration-300 hover:-translate-x-1 hover:text-primary">
                            <div class="w-6 h-6 rounded-lg bg-green-100 flex items-center justify-center text-green-600 mr-3 shadow-sm">
                                <svg class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <span class="text-gray-700 font-medium">Bed frames & headboards</span>
                        </li>
                        <li class="flex items-center transform transition-all duration-300 hover:-translate-x-1 hover:text-primary">
                            <div class="w-6 h-6 rounded-lg bg-green-100 flex items-center justify-center text-green-600 mr-3 shadow-sm">
                                <svg class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <span class="text-gray-700 font-medium">Proper disposal methods</span>
                        </li>
                    </ul>

                    <!-- Enhanced Price Indicator -->
                    <div class="flex items-center justify-between mb-6 bg-gray-50 p-4 rounded-xl">
                        <div class="text-gray-500 text-sm font-medium">Starting from</div>
                        <div class="text-primary font-bold text-xl">AED 250</div>
                    </div>

                    <!-- Enhanced CTA Button -->
                    <a href="/booking" class="block text-center py-3.5 px-4 bg-gray-100 text-gray-800 font-semibold rounded-xl
                       hover:bg-primary hover:text-white transition-all duration-300 transform group-hover:shadow-md relative overflow-hidden">
                        <span class="relative z-10">Book Now</span>
                        <div class="absolute inset-0 bg-primary transform scale-x-0 origin-left transition-transform duration-500 group-hover:scale-x-100 -z-1"></div>
                    </a>
                </div>
            </div>

            <!-- Enhanced Service Card 2 -->
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-500 hover:-translate-y-3 hover:shadow-xl group card-hover-effect">
                <!-- Enhanced Card Header with Image and Overlay -->
                <div class="h-56 bg-cover bg-center relative overflow-hidden">
                    <!-- Image with Zoom Effect -->
                    <div class="absolute inset-0 bg-cover bg-center transform transition-transform duration-700 group-hover:scale-110"
                         style="background-image: url('/image2.jpg');">
                    </div>

                    <!-- Gradient Overlay -->
                    <div class="absolute inset-0 bg-gradient-to-b from-transparent via-black/20 to-black/70 opacity-80 transition-opacity duration-500 group-hover:opacity-90"></div>

                    <!-- Badge with Animation -->
                    <div class="absolute top-4 left-4 z-10 transform transition-transform duration-500 group-hover:scale-110 group-hover:rotate-3">
                        <span class="bg-green-500 text-white text-xs font-bold px-4 py-1.5 rounded-full shadow-md inline-flex items-center">
                            <span class="w-1.5 h-1.5 bg-white rounded-full mr-1.5 animate-pulse"></span>
                            ECO-FRIENDLY
                        </span>
                    </div>

                    <!-- Card Title Overlay -->
                    <div class="absolute bottom-0 left-0 right-0 p-6 z-10 transform translate-y-5 transition-transform duration-500 group-hover:translate-y-0">
                        <h3 class="text-xl font-bold text-white mb-1 group-hover:text-primary-light transition-colors duration-300">Sofa & Couch Removal</h3>
                        <div class="w-10 h-1 bg-primary rounded-full transform scale-x-0 origin-left transition-transform duration-500 group-hover:scale-x-100"></div>
                    </div>
                </div>

                <!-- Enhanced Card Content -->
                <div class="p-6 pt-8 relative">
                    <!-- Floating Icon -->
                    <div class="w-16 h-16 bg-primary-ultra-light rounded-2xl flex items-center justify-center text-primary -mt-16 mb-6
                         shadow-lg border-4 border-white absolute right-6 transform transition-all duration-500
                         group-hover:bg-primary group-hover:text-white group-hover:rotate-6 group-hover:scale-110">
                        <i class="fas fa-couch text-xl"></i>
                    </div>

                    <!-- Card Description -->
                    <p class="text-gray-600 mb-6 mt-6">
                        Expert removal of all couch types with doorway protection and careful handling by our professional team.
                    </p>

                    <!-- Enhanced Features List -->
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center transform transition-all duration-300 hover:-translate-x-1 hover:text-primary">
                            <div class="w-6 h-6 rounded-lg bg-green-100 flex items-center justify-center text-green-600 mr-3 shadow-sm">
                                <svg class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <span class="text-gray-700 font-medium">Sectional couches</span>
                        </li>
                        <li class="flex items-center transform transition-all duration-300 hover:-translate-x-1 hover:text-primary">
                            <div class="w-6 h-6 rounded-lg bg-green-100 flex items-center justify-center text-green-600 mr-3 shadow-sm">
                                <svg class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <span class="text-gray-700 font-medium">Sleeper sofas & futons</span>
                        </li>
                        <li class="flex items-center transform transition-all duration-300 hover:-translate-x-1 hover:text-primary">
                            <div class="w-6 h-6 rounded-lg bg-green-100 flex items-center justify-center text-green-600 mr-3 shadow-sm">
                                <svg class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <span class="text-gray-700 font-medium">Doorway protection</span>
                        </li>
                    </ul>

                    <!-- Enhanced Price Indicator -->
                    <div class="flex items-center justify-between mb-6 bg-gray-50 p-4 rounded-xl">
                        <div class="text-gray-500 text-sm font-medium">Starting from</div>
                        <div class="text-primary font-bold text-xl">AED 300</div>
                    </div>

                    <!-- Enhanced CTA Button -->
                    <a href="/booking" class="block text-center py-3.5 px-4 bg-gray-100 text-gray-800 font-semibold rounded-xl
                       hover:bg-primary hover:text-white transition-all duration-300 transform group-hover:shadow-md relative overflow-hidden">
                        <span class="relative z-10">Book Now</span>
                        <div class="absolute inset-0 bg-primary transform scale-x-0 origin-left transition-transform duration-500 group-hover:scale-x-100 -z-1"></div>
                    </a>
                </div>
            </div>

            <!-- Enhanced Service Card 3 -->
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-500 hover:-translate-y-3 hover:shadow-xl group card-hover-effect">
                <!-- Enhanced Card Header with Image and Overlay -->
                <div class="h-56 bg-cover bg-center relative overflow-hidden">
                    <!-- Image with Zoom Effect -->
                    <div class="absolute inset-0 bg-cover bg-center transform transition-transform duration-700 group-hover:scale-110"
                         style="background-image: url('/image4.jpg');">
                    </div>

                    <!-- Gradient Overlay -->
                    <div class="absolute inset-0 bg-gradient-to-b from-transparent via-black/20 to-black/70 opacity-80 transition-opacity duration-500 group-hover:opacity-90"></div>

                    <!-- Badge with Animation -->
                    <div class="absolute top-4 left-4 z-10 transform transition-transform duration-500 group-hover:scale-110 group-hover:rotate-3">
                        <span class="bg-blue-500 text-white text-xs font-bold px-4 py-1.5 rounded-full shadow-md inline-flex items-center">
                            <span class="w-1.5 h-1.5 bg-white rounded-full mr-1.5 animate-pulse"></span>
                            COMMERCIAL
                        </span>
                    </div>

                    <!-- Card Title Overlay -->
                    <div class="absolute bottom-0 left-0 right-0 p-6 z-10 transform translate-y-5 transition-transform duration-500 group-hover:translate-y-0">
                        <h3 class="text-xl font-bold text-white mb-1 group-hover:text-primary-light transition-colors duration-300">Office Clearance</h3>
                        <div class="w-10 h-1 bg-primary rounded-full transform scale-x-0 origin-left transition-transform duration-500 group-hover:scale-x-100"></div>
                    </div>
                </div>

                <!-- Enhanced Card Content -->
                <div class="p-6 pt-8 relative">
                    <!-- Floating Icon -->
                    <div class="w-16 h-16 bg-primary-ultra-light rounded-2xl flex items-center justify-center text-primary -mt-16 mb-6
                         shadow-lg border-4 border-white absolute right-6 transform transition-all duration-500
                         group-hover:bg-primary group-hover:text-white group-hover:rotate-6 group-hover:scale-110">
                        <i class="fas fa-building text-xl"></i>
                    </div>

                    <!-- Card Description -->
                    <p class="text-gray-600 mb-6 mt-6">
                        Complete office furniture and equipment removal services for businesses of all sizes with minimal disruption.
                    </p>

                    <!-- Enhanced Features List -->
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center transform transition-all duration-300 hover:-translate-x-1 hover:text-primary">
                            <div class="w-6 h-6 rounded-lg bg-green-100 flex items-center justify-center text-green-600 mr-3 shadow-sm">
                                <svg class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <span class="text-gray-700 font-medium">Desks & workstations</span>
                        </li>
                        <li class="flex items-center transform transition-all duration-300 hover:-translate-x-1 hover:text-primary">
                            <div class="w-6 h-6 rounded-lg bg-green-100 flex items-center justify-center text-green-600 mr-3 shadow-sm">
                                <svg class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <span class="text-gray-700 font-medium">Filing cabinets & storage</span>
                        </li>
                        <li class="flex items-center transform transition-all duration-300 hover:-translate-x-1 hover:text-primary">
                            <div class="w-6 h-6 rounded-lg bg-green-100 flex items-center justify-center text-green-600 mr-3 shadow-sm">
                                <svg class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <span class="text-gray-700 font-medium">Electronics recycling</span>
                        </li>
                    </ul>

                    <!-- Enhanced Price Indicator -->
                    <div class="flex items-center justify-between mb-6 bg-gray-50 p-4 rounded-xl">
                        <div class="text-gray-500 text-sm font-medium">Starting from</div>
                        <div class="text-primary font-bold text-xl">AED 500</div>
                    </div>

                    <!-- Enhanced CTA Button -->
                    <a href="/booking" class="block text-center py-3.5 px-4 bg-gray-100 text-gray-800 font-semibold rounded-xl
                       hover:bg-primary hover:text-white transition-all duration-300 transform group-hover:shadow-md relative overflow-hidden">
                        <span class="relative z-10">Book Now</span>
                        <div class="absolute inset-0 bg-primary transform scale-x-0 origin-left transition-transform duration-500 group-hover:scale-x-100 -z-1"></div>
                    </a>
                </div>
            </div>
        </div>

        <!-- Enhanced View All Services Button -->
        <div class="text-center mt-16" data-aos="fade-up" data-aos-delay="200">
            <a href="/services" class="inline-flex items-center justify-center px-10 py-4 bg-white text-primary font-semibold rounded-xl shadow-lg border border-primary-ultra-light hover:bg-primary-ultra-light transition-all duration-500 transform hover:-translate-y-2 hover:shadow-xl relative overflow-hidden group">
                <span class="relative z-10">View All Services</span>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 relative z-10 transition-transform duration-500 transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
                <div class="absolute top-0 left-0 w-full h-full bg-primary opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>
            </a>

            <!-- Decorative Elements -->
            <div class="relative mt-8 opacity-30">
                <div class="absolute left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <div class="flex space-x-2">
                        <div class="w-2 h-2 rounded-full bg-primary"></div>
                        <div class="w-2 h-2 rounded-full bg-primary"></div>
                        <div class="w-2 h-2 rounded-full bg-primary"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

    <!-- Enhanced Image Gallery with Dynamic Layout -->
    <section class="py-20 relative overflow-hidden" id="gallery">
        <!-- Background Elements -->
        <div class="absolute inset-0 bg-gradient-to-b from-orange-500 to-orange-600 opacity-90"></div>
        <div class="absolute top-0 left-0 w-full h-64 bg-white" style="clip-path: ellipse(75% 100% at center top);"></div>
        <div class="absolute bottom-0 left-0 w-full h-64 bg-white" style="clip-path: ellipse(75% 100% at center bottom);"></div>

        <!-- Content Container -->
        <div class="container mx-auto px-4 sm:px-6 relative z-10">
            <!-- Section Header -->
            <div class="text-center mb-16" data-aos="fade-up">
                <div class="inline-block px-3 py-1 bg-white text-orange-600 rounded-full text-sm font-semibold mb-4 shadow-md">
                    OUR WORK
                </div>
                <h2 class="text-3xl md:text-4xl font-extrabold mb-6 text-white">
                    Project Gallery
                </h2>
                <p class="text-white/80 max-w-2xl mx-auto text-lg">
                    See our team in action across Dubai and the UAE, providing professional waste removal services
                </p>
            </div>

            <!-- Gallery Grid with Masonry-like Layout -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6" data-aos="fade-up" data-aos-delay="100">
                <!-- Featured Large Image -->
                <div class="col-span-2 row-span-2 relative group">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end justify-start p-6">
                        <div class="text-white">
                            <h3 class="font-bold text-xl mb-1">Commercial Clearance</h3>
                            <p class="text-white/80">Office building in Dubai Marina</p>
                        </div>
                    </div>
                    <img
                        src="/image1.jpg"
                        alt="Commercial Clearance"
                        class="w-full h-full object-cover rounded-2xl shadow-xl transform transition-transform duration-500 group-hover:scale-105"
                        loading="lazy"
                    >
                </div>

                <!-- Regular Images -->
                <div class="relative group overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end justify-start p-4">
                        <div class="text-white">
                            <h3 class="font-bold text-sm">Furniture Removal</h3>
                        </div>
                    </div>
                    <img
                        src="/image2.jpg"
                        alt="Furniture Removal"
                        class="w-full h-full object-cover rounded-2xl shadow-xl transform transition-transform duration-500 group-hover:scale-110"
                        loading="lazy"
                    >
                </div>

                <div class="relative group overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end justify-start p-4">
                        <div class="text-white">
                            <h3 class="font-bold text-sm">Mattress Disposal</h3>
                        </div>
                    </div>
                    <img
                        src="/image3.jpg"
                        alt="Mattress Disposal"
                        class="w-full h-full object-cover rounded-2xl shadow-xl transform transition-transform duration-500 group-hover:scale-110"
                        loading="lazy"
                    >
                </div>

                <div class="relative group overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end justify-start p-4">
                        <div class="text-white">
                            <h3 class="font-bold text-sm">Residential Cleanup</h3>
                        </div>
                    </div>
                    <img
                        src="/image4.jpg"
                        alt="Residential Cleanup"
                        class="w-full h-full object-cover rounded-2xl shadow-xl transform transition-transform duration-500 group-hover:scale-110"
                        loading="lazy"
                    >
                </div>

                <div class="relative group overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end justify-start p-4">
                        <div class="text-white">
                            <h3 class="font-bold text-sm">Construction Debris</h3>
                        </div>
                    </div>
                    <img
                        src="/image5.jpg"
                        alt="Construction Debris"
                        class="w-full h-full object-cover rounded-2xl shadow-xl transform transition-transform duration-500 group-hover:scale-110"
                        loading="lazy"
                    >
                </div>

                <!-- Featured Medium Image -->
                <div class="col-span-2 relative group overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end justify-start p-6">
                        <div class="text-white">
                            <h3 class="font-bold text-xl mb-1">Eco-Friendly Disposal</h3>
                            <p class="text-white/80">Recycling center partnership</p>
                        </div>
                    </div>
                    <img
                        src="/image6.jpg"
                        alt="Eco-Friendly Disposal"
                        class="w-full h-full object-cover rounded-2xl shadow-xl transform transition-transform duration-500 group-hover:scale-105"
                        loading="lazy"
                    >
                </div>

                <!-- More Regular Images -->
                <div class="relative group overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end justify-start p-4">
                        <div class="text-white">
                            <h3 class="font-bold text-sm">Office Clearance</h3>
                        </div>
                    </div>
                    <img
                        src="/image7.jpg"
                        alt="Office Clearance"
                        class="w-full h-full object-cover rounded-2xl shadow-xl transform transition-transform duration-500 group-hover:scale-110"
                        loading="lazy"
                    >
                </div>

                <div class="relative group overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end justify-start p-4">
                        <div class="text-white">
                            <h3 class="font-bold text-sm">Appliance Removal</h3>
                        </div>
                    </div>
                    <img
                        src="/image8.jpg"
                        alt="Appliance Removal"
                        class="w-full h-full object-cover rounded-2xl shadow-xl transform transition-transform duration-500 group-hover:scale-110"
                        loading="lazy"
                    >
                </div>
            </div>

            <!-- View More Button -->
            <div class="text-center mt-12" data-aos="fade-up" data-aos-delay="200">
                <a href="/gallery" class="inline-flex items-center justify-center px-8 py-4 bg-white text-orange-600 font-semibold rounded-xl shadow-lg hover:bg-orange-50 transition-all duration-300">
                    View Full Gallery
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                </a>
            </div>
        </div>
    </section>


    <!-- Enhanced Process Section with Interactive Elements -->
    <section class="py-24 bg-gray-50 relative overflow-hidden" id="process">
        <!-- Background Elements -->
        <div class="absolute top-0 right-0 w-64 h-64 bg-orange-100 rounded-full opacity-50 -translate-y-1/2 translate-x-1/2"></div>
        <div class="absolute bottom-0 left-0 w-96 h-96 bg-orange-100 rounded-full opacity-50 translate-y-1/2 -translate-x-1/2"></div>

        <div class="container mx-auto px-4 sm:px-6 relative z-10">
            <!-- Section Header with Enhanced Typography -->
            <div class="text-center mb-20" data-aos="fade-up">
                <div class="inline-block px-3 py-1 bg-orange-100 text-orange-600 rounded-full text-sm font-semibold mb-4">
                    SIMPLE PROCESS
                </div>
                <h2 class="text-3xl md:text-4xl font-extrabold mb-6 relative inline-block">
                    How It Works
                    <span class="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-orange-500 rounded-full"></span>
                </h2>
                <p class="text-gray-600 max-w-2xl mx-auto text-lg">
                    Our streamlined three-step process makes junk removal quick and hassle-free
                </p>
            </div>

            <!-- Process Steps with Connection Lines -->
            <div class="relative">
                <!-- Connection Lines (Desktop Only) -->
                <div class="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-orange-200"></div>
                <div class="hidden md:block absolute top-1/2 left-1/4 w-0.5 h-16 bg-orange-200 -translate-x-1/2 -translate-y-full"></div>
                <div class="hidden md:block absolute top-1/2 left-1/2 w-0.5 h-16 bg-orange-200 -translate-x-1/2 -translate-y-full"></div>
                <div class="hidden md:block absolute top-1/2 left-3/4 w-0.5 h-16 bg-orange-200 -translate-x-1/2 -translate-y-full"></div>

                <!-- Process Steps -->
                <div class="grid md:grid-cols-3 gap-12 md:gap-6 relative z-10">
                    <!-- Step 1 -->
                    <div class="bg-white rounded-2xl shadow-xl p-8 text-center relative transform transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up">
                        <!-- Step Number -->
                        <div class="absolute -top-8 left-1/2 transform -translate-x-1/2">
                            <div class="w-16 h-16 rounded-full bg-orange-500 text-white flex items-center justify-center text-2xl font-bold shadow-lg border-4 border-white">
                                1
                            </div>
                        </div>

                        <!-- Step Icon -->
                        <div class="w-20 h-20 bg-orange-100 rounded-2xl flex items-center justify-center text-orange-600 mx-auto mb-6 mt-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                        </div>

                        <!-- Step Content -->
                        <h3 class="text-xl font-bold mb-4 text-gray-800">Schedule Online</h3>
                        <p class="text-gray-600 mb-6">
                            Book your appointment through our easy online form or give us a call. Select your preferred date and time.
                        </p>

                        <!-- Features List -->
                        <ul class="text-left space-y-3 mb-6">
                            <li class="flex items-center">
                                <div class="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3 flex-shrink-0">
                                    <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <span class="text-gray-700 text-sm">24/7 online booking</span>
                            </li>
                            <li class="flex items-center">
                                <div class="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3 flex-shrink-0">
                                    <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <span class="text-gray-700 text-sm">Instant price estimate</span>
                            </li>
                        </ul>

                        <!-- Action Button -->
                        <a href="/booking" class="inline-flex items-center text-orange-600 font-medium hover:text-orange-700 transition-colors">
                            Book Now
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                            </svg>
                        </a>
                    </div>

                    <!-- Step 2 -->
                    <div class="bg-white rounded-2xl shadow-xl p-8 text-center relative transform transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-delay="100">
                        <!-- Step Number -->
                        <div class="absolute -top-8 left-1/2 transform -translate-x-1/2">
                            <div class="w-16 h-16 rounded-full bg-orange-500 text-white flex items-center justify-center text-2xl font-bold shadow-lg border-4 border-white">
                                2
                            </div>
                        </div>

                        <!-- Step Icon -->
                        <div class="w-20 h-20 bg-orange-100 rounded-2xl flex items-center justify-center text-orange-600 mx-auto mb-6 mt-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                            </svg>
                        </div>

                        <!-- Step Content -->
                        <h3 class="text-xl font-bold mb-4 text-gray-800">We Remove Your Junk</h3>
                        <p class="text-gray-600 mb-6">
                            Our professional team arrives on time, ready to handle all the heavy lifting and proper disposal.
                        </p>

                        <!-- Features List -->
                        <ul class="text-left space-y-3 mb-6">
                            <li class="flex items-center">
                                <div class="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3 flex-shrink-0">
                                    <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <span class="text-gray-700 text-sm">Uniformed, trained staff</span>
                            </li>
                            <li class="flex items-center">
                                <div class="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3 flex-shrink-0">
                                    <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <span class="text-gray-700 text-sm">No hidden fees</span>
                            </li>
                        </ul>

                        <!-- Action Button -->
                        <a href="/services" class="inline-flex items-center text-orange-600 font-medium hover:text-orange-700 transition-colors">
                            Our Services
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                            </svg>
                        </a>
                    </div>

                    <!-- Step 3 -->
                    <div class="bg-white rounded-2xl shadow-xl p-8 text-center relative transform transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-delay="200">
                        <!-- Step Number -->
                        <div class="absolute -top-8 left-1/2 transform -translate-x-1/2">
                            <div class="w-16 h-16 rounded-full bg-orange-500 text-white flex items-center justify-center text-2xl font-bold shadow-lg border-4 border-white">
                                3
                            </div>
                        </div>

                        <!-- Step Icon -->
                        <div class="w-20 h-20 bg-orange-100 rounded-2xl flex items-center justify-center text-orange-600 mx-auto mb-6 mt-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>

                        <!-- Step Content -->
                        <h3 class="text-xl font-bold mb-4 text-gray-800">Enjoy Your Clean Space</h3>
                        <p class="text-gray-600 mb-6">
                            Relax and enjoy your newly cleared space while we handle the responsible disposal of your items.
                        </p>

                        <!-- Features List -->
                        <ul class="text-left space-y-3 mb-6">
                            <li class="flex items-center">
                                <div class="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3 flex-shrink-0">
                                    <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <span class="text-gray-700 text-sm">Eco-friendly disposal</span>
                            </li>
                            <li class="flex items-center">
                                <div class="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3 flex-shrink-0">
                                    <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <span class="text-gray-700 text-sm">Satisfaction guaranteed</span>
                            </li>
                        </ul>

                        <!-- Action Button -->
                        <a href="/about" class="inline-flex items-center text-orange-600 font-medium hover:text-orange-700 transition-colors">
                            Learn More
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                            </svg>
                        </a>
                    </div>
                </div>
            </div>

            <!-- CTA Button -->
            <div class="text-center mt-16" data-aos="fade-up" data-aos-delay="300">
                <a href="/booking" class="inline-flex items-center justify-center px-8 py-4 bg-orange-500 text-white font-semibold rounded-xl shadow-lg hover:bg-orange-600 transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl">
                    Get Started Today
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- Home Furniture & Junk Removal Services Section -->
    <section class="py-16 md:py-20 bg-gray-50" id="home-junk-removal">
        <div class="container mx-auto px-4 sm:px-6">
            <div class="text-center mb-12" data-aos="fade-up">
                <div class="inline-block p-3 bg-orange-100 rounded-xl mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                </div>
                <h2 class="text-3xl sm:text-4xl font-extrabold mb-4">Home Furniture & Junk Removal</h2>
                <p class="text-gray-600 text-lg max-w-3xl mx-auto">
                    We make getting rid of old furniture and household junk stress-free with our professional removal services.
                </p>
            </div>

            <!-- Split Content Section -->
            <div class="grid md:grid-cols-2 gap-8 mb-12">
                <!-- Left Content with Image -->
                <div class="rounded-xl overflow-hidden shadow-lg" data-aos="fade-right">
                    <img src="/image3.jpg" alt="Furniture Removal Services" class="w-full h-80 object-cover">
                </div>

                <!-- Right Content with Text -->
                <div class="flex flex-col justify-center" data-aos="fade-left">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6">Professional Furniture Removal</h3>
                    <p class="text-gray-600 mb-6">
                        Our furniture removal service is designed to take the hassle out of disposing unwanted furniture. Whether you're replacing old pieces, downsizing, or completing a home renovation, our team will efficiently remove and properly dispose of your furniture items.
                    </p>

                    <div class="space-y-4 mb-8">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-6 w-6 rounded-full bg-green-100 flex items-center justify-center mt-1 mr-3">
                                <svg class="h-4 w-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <div>
                                <h4 class="text-lg font-medium text-gray-800">All Furniture Types</h4>
                                <p class="text-gray-600">Sofas, beds, dressers, tables, chairs, entertainment centers, and more</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-6 w-6 rounded-full bg-green-100 flex items-center justify-center mt-1 mr-3">
                                <svg class="h-4 w-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <div>
                                <h4 class="text-lg font-medium text-gray-800">Eco-Friendly Disposal</h4>
                                <p class="text-gray-600">We donate usable items and recycle materials whenever possible</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-6 w-6 rounded-full bg-green-100 flex items-center justify-center mt-1 mr-3">
                                <svg class="h-4 w-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <div>
                                <h4 class="text-lg font-medium text-gray-800">Heavy Lifting Included</h4>
                                <p class="text-gray-600">Our professional team handles all the heavy lifting and stairs</p>
                            </div>
                        </div>
                    </div>

                    <a href="/booking" class="bg-orange-500 text-white rounded-lg px-6 py-3 inline-flex items-center justify-center hover:bg-orange-600 transition w-full md:w-auto font-medium">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        Book Furniture Removal
                    </a>
                </div>
            </div>

            <!-- Service Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12" data-aos="fade-up">
                <!-- Residential Junk Removal Card -->
                <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow">
                    <div class="p-6">
                        <div class="w-14 h-14 bg-orange-100 rounded-full flex items-center justify-center mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-4 text-gray-800">Residential Junk Removal</h3>
                        <p class="text-gray-600 mb-6">
                            We help homeowners clear out unwanted items from apartments, houses, garages, and yards. Our team handles everything from single items to complete property cleanouts.
                        </p>
                        <ul class="mb-8 space-y-2 text-gray-600">
                            <li class="flex items-center">
                                <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Furniture & appliances
                            </li>
                            <li class="flex items-center">
                                <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Electronics disposal
                            </li>
                            <li class="flex items-center">
                                <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Yard waste removal
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Estate Cleanout Card -->
                <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow">
                    <div class="p-6">
                        <div class="w-14 h-14 bg-orange-100 rounded-full flex items-center justify-center mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-4 text-gray-800">Estate Cleanout Services</h3>
                        <p class="text-gray-600 mb-6">
                            Our compassionate team provides thorough estate cleanout services for families dealing with the loss of a loved one or needing to prepare a property for sale.
                        </p>
                        <ul class="mb-8 space-y-2 text-gray-600">
                            <li class="flex items-center">
                                <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Complete property clearance
                            </li>
                            <li class="flex items-center">
                                <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Donation coordination
                            </li>
                            <li class="flex items-center">
                                <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Respectful handling of personal items
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Moving Junk Card -->
                <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow">
                    <div class="p-6">
                        <div class="w-14 h-14 bg-orange-100 rounded-full flex items-center justify-center mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-4 text-gray-800">Moving Cleanout Services</h3>
                        <p class="text-gray-600 mb-6">
                            When you're moving, let us handle the items you don't want to take with you. We'll remove unwanted furniture and junk so you can focus on settling into your new home.
                        </p>
                        <ul class="mb-8 space-y-2 text-gray-600">
                            <li class="flex items-center">
                                <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Pre-move junk removal
                            </li>
                            <li class="flex items-center">
                                <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Old furniture disposal
                            </li>
                            <li class="flex items-center">
                                <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Move-in preparations
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Benefits Section -->
            <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl p-8 md:p-12 text-white shadow-xl" data-aos="fade-up">
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-2xl font-bold mb-6">Why Choose Our Junk Removal Service?</h3>
                        <p class="mb-6">
                            With our professional equipment and trained team, we make furniture and junk removal quick, easy, and hassle-free for homeowners and businesses throughout Dubai and the UAE.
                        </p>
                        <div class="grid grid-cols-2 gap-4 mb-6">
                            <div class="flex items-center">
                                <svg class="h-6 w-6 mr-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                </svg>
                                <span>Licensed & Insured</span>
                            </div>
                            <div class="flex items-center">
                                <svg class="h-6 w-6 mr-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span>Same-Day Service</span>
                            </div>
                            <div class="flex items-center">
                                <svg class="h-6 w-6 mr-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                                </svg>
                                <span>Upfront Pricing</span>
                            </div>
                            <div class="flex items-center">
                                <svg class="h-6 w-6 mr-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11.5V14m0-2.5v-6a1.5 1.5 0 113 0m-3 6a1.5 1.5 0 00-3 0v2a7.5 7.5 0 0015 0v-5a1.5 1.5 0 00-3 0m-6-3V11m0-5.5v-1a1.5 1.5 0 013 0v1m0 0V11m0-5.5a1.5 1.5 0 013 0v3m0 0V11" />
                                </svg>
                                <span>No Hidden Fees</span>
                            </div>
                        </div>
                        <a href="/booking" class="inline-block bg-white text-orange-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition mt-4">
                            Get a Free Quote
                        </a>
                    </div>
                    <div class="flex items-center justify-center">
                        <img src="/image6.jpg" alt="Junk Removal Team" class="rounded-lg shadow-lg max-h-80 object-cover">
                    </div>
                </div>
            </div>
        </div>
    </section>

  <!-- Enhanced CTA Section with Dynamic Elements -->
<section class="relative py-24 overflow-hidden" id="cta">
    <!-- Background Elements -->
    <div class="absolute inset-0 bg-gradient-to-br from-orange-600 to-orange-800"></div>

    <!-- Decorative Shapes -->
    <div class="absolute top-0 left-0 w-full h-20 bg-white" style="clip-path: polygon(0 0, 100% 0, 100% 100%, 0 0);"></div>
    <div class="absolute bottom-0 right-0 w-full h-20 bg-white" style="clip-path: polygon(100% 100%, 0 100%, 100% 0);"></div>

    <!-- Floating Elements -->
    <div class="absolute top-1/4 left-10 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
    <div class="absolute bottom-1/4 right-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>

    <!-- Pattern Overlay -->
    <div class="absolute inset-0 opacity-10" style="background-image: url('data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'1\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>

    <!-- Content Container -->
    <div class="relative container mx-auto px-4 sm:px-6 z-10">
        <div class="max-w-4xl mx-auto">
            <!-- Two-Column Layout -->
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <!-- Left Column: Content -->
                <div class="text-white" data-aos="fade-right">
                    <div class="inline-block px-3 py-1 bg-white/20 text-white rounded-full text-sm font-semibold mb-6 backdrop-blur-sm">
                        TAKE ACTION NOW
                    </div>
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-extrabold mb-6 leading-tight">
                        Ready to Clear Your Space?
                    </h2>
                    <p class="text-xl text-white/80 mb-8 leading-relaxed">
                        Get instant pricing and book your junk removal in under 2 minutes. Our team is ready to help you reclaim your space.
                    </p>

                    <!-- Benefits List -->
                    <div class="mb-8 space-y-4">
                        <div class="flex items-center">
                            <div class="w-6 h-6 rounded-full bg-white/20 flex items-center justify-center mr-3">
                                <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <span class="text-white/90">Same-day service available</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-6 h-6 rounded-full bg-white/20 flex items-center justify-center mr-3">
                                <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <span class="text-white/90">Upfront, transparent pricing</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-6 h-6 rounded-full bg-white/20 flex items-center justify-center mr-3">
                                <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <span class="text-white/90">Professional, uniformed team</span>
                        </div>
                    </div>

                    <!-- CTA Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="/booking" class="relative group overflow-hidden rounded-xl bg-white text-orange-600 px-8 py-4 font-bold shadow-lg inline-flex items-center justify-center">
                            <span class="relative z-10">Book Now</span>
                            <div class="absolute inset-0 bg-gray-100 translate-y-full group-hover:translate-y-0 transition-transform duration-300"></div>
                        </a>
                        <a href="tel:+971569257614" class="relative group overflow-hidden rounded-xl bg-transparent border-2 border-white text-white px-8 py-4 font-bold inline-flex items-center justify-center">
                            <span class="relative z-10">Call Us</span>
                            <div class="absolute inset-0 bg-white/20 translate-y-full group-hover:translate-y-0 transition-transform duration-300"></div>
                        </a>
                    </div>
                </div>

                <!-- Right Column: Floating Card -->
                <div class="relative" data-aos="fade-up">
                    <!-- Decorative Elements -->
                    <div class="absolute -top-6 -left-6 w-24 h-24 bg-orange-300 rounded-full opacity-50 blur-xl"></div>
                    <div class="absolute -bottom-6 -right-6 w-32 h-32 bg-orange-300 rounded-full opacity-50 blur-xl"></div>

                    <!-- Card -->
                    <div class="relative bg-white rounded-2xl shadow-2xl p-8 transform transition-all duration-500 hover:-translate-y-2 hover:shadow-orange-500/20">
                        <div class="absolute -top-5 -right-5 bg-orange-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                            LIMITED TIME
                        </div>

                        <h3 class="text-2xl font-bold text-gray-800 mb-4">Special Offer</h3>
                        <p class="text-gray-600 mb-6">Book your junk removal service today and receive:</p>

                        <div class="space-y-4 mb-8">
                            <div class="flex items-start">
                                <div class="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center text-green-600 mt-0.5 mr-3 flex-shrink-0">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-800">10% Off First Booking</h4>
                                    <p class="text-gray-500 text-sm">For new customers</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <div class="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center text-green-600 mt-0.5 mr-3 flex-shrink-0">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-800">Free Site Assessment</h4>
                                    <p class="text-gray-500 text-sm">Accurate quote before work begins</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <div class="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center text-green-600 mt-0.5 mr-3 flex-shrink-0">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-800">Priority Scheduling</h4>
                                    <p class="text-gray-500 text-sm">Get serviced faster</p>
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <span class="block text-gray-500 text-sm mb-1">Offer expires in</span>
                            <div class="flex justify-center gap-2 mb-6">
                                <div class="bg-gray-100 rounded-lg px-3 py-2 w-16">
                                    <span class="block text-2xl font-bold text-gray-800">24</span>
                                    <span class="text-xs text-gray-500">Hours</span>
                                </div>
                                <div class="bg-gray-100 rounded-lg px-3 py-2 w-16">
                                    <span class="block text-2xl font-bold text-gray-800">00</span>
                                    <span class="text-xs text-gray-500">Minutes</span>
                                </div>
                                <div class="bg-gray-100 rounded-lg px-3 py-2 w-16">
                                    <span class="block text-2xl font-bold text-gray-800">00</span>
                                    <span class="text-xs text-gray-500">Seconds</span>
                                </div>
                            </div>

                            <a href="/booking" class="block w-full bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-6 rounded-xl transition-colors">
                                Claim Offer Now
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Location Map Section -->
<section class="py-16 md:py-20 bg-gray-50">
    <div class="container mx-auto px-4 sm:px-6">
        <div class="text-center mb-12 md:mb-16" data-aos="fade-up">
            <h2 class="text-3xl sm:text-4xl font-extrabold mb-4">Visit Our Location</h2>
            <p class="text-gray-600 text-base sm:text-lg max-w-3xl mx-auto">
                We're conveniently located in Al Quoz, Dubai. Stop by our office to discuss your junk removal needs in person.
            </p>
        </div>

        <div class="grid md:grid-cols-5 gap-0 rounded-2xl shadow-xl overflow-hidden" data-aos="fade-up">
            <!-- Location Info -->
            <div class="md:col-span-2 p-8 md:p-10 flex flex-col justify-center bg-orange-50">
                <div class="mb-6">
                    <div class="inline-block p-3 bg-orange-100 rounded-xl mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Our Office</h3>
                    <p class="text-gray-600 mb-6">Our Al Quoz office is centrally located and easily accessible. Come visit us for an in-person consultation!</p>

                    <div class="space-y-4 mb-8">
                        <div class="flex items-center text-gray-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                            </svg>
                            <span>22nd St, Al Quoz, Dubai</span>
                        </div>
                        <div class="flex items-center text-gray-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                            </svg>
                            <span>+971 56 925 7614</span>
                        </div>
                        <div class="flex items-center text-gray-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>Monday - Saturday: 8AM - 8PM<br>Sunday: 9AM - 5PM</span>
                        </div>
                    </div>

                    <a href="/contact" class="inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-lg bg-orange-500 hover:bg-orange-600 text-white font-medium transition-colors">
                        Contact Us
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Map Display -->
            <div class="md:col-span-3 h-full min-h-[400px] relative overflow-hidden">
                <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3612.373828575674!2d55.2214023!3d25.1230494!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e5f695fbe01480f%3A0x2d32b7010163efdd!2s22nd%20St%20-%20Al%20Quoz%20-%20Dubai%20-%20United%20Arab%20Emirates!5e0!3m2!1sen!2s!4v1745078640673!5m2!1sen!2s"
                    width="100%"
                    height="100%"
                    style="border:0;"
                    allowfullscreen=""
                    loading="lazy"
                    referrerpolicy="no-referrer-when-downgrade">
                </iframe>
            </div>
        </div>
        </div>
    </section>

<%- include('partials/footer') %>
